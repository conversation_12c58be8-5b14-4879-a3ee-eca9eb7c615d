"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.asyncHandler = exports.notFoundHandler = exports.errorHandler = void 0;
const errors_1 = require("../utils/errors");
const logger_1 = require("../utils/logger");
const errorHandler = (error, req, res, next) => {
    logger_1.logger.error('Request error', {
        error: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent')
    });
    if (error instanceof errors_1.AppError) {
        return res.status(error.statusCode).json((0, errors_1.formatErrorResponse)(error));
    }
    if (error.name === 'MulterError') {
        const multerError = error;
        let message = 'File upload error';
        let statusCode = 400;
        switch (multerError.code) {
            case 'LIMIT_FILE_SIZE':
                message = 'File size exceeds the maximum limit';
                break;
            case 'LIMIT_FILE_COUNT':
                message = 'Too many files uploaded';
                break;
            case 'LIMIT_UNEXPECTED_FILE':
                message = 'Unexpected file field';
                break;
            case 'LIMIT_PART_COUNT':
                message = 'Too many parts in multipart form';
                break;
            case 'LIMIT_FIELD_KEY':
                message = 'Field name too long';
                break;
            case 'LIMIT_FIELD_VALUE':
                message = 'Field value too long';
                break;
            case 'LIMIT_FIELD_COUNT':
                message = 'Too many fields';
                break;
            default:
                message = multerError.message || 'File upload error';
        }
        return res.status(statusCode).json({
            success: false,
            error: {
                code: 'FILE_UPLOAD_ERROR',
                message,
                details: { multerCode: multerError.code }
            }
        });
    }
    if (error.name === 'SyntaxError' && 'body' in error) {
        return res.status(400).json({
            success: false,
            error: {
                code: 'INVALID_JSON',
                message: 'Invalid JSON in request body'
            }
        });
    }
    if (error.name === 'ValidationError') {
        return res.status(400).json({
            success: false,
            error: {
                code: 'VALIDATION_ERROR',
                message: error.message
            }
        });
    }
    const isProduction = process.env.NODE_ENV === 'production';
    return res.status(500).json({
        success: false,
        error: {
            code: 'INTERNAL_ERROR',
            message: isProduction ? 'Internal server error' : error.message,
            details: isProduction ? undefined : error.stack
        }
    });
};
exports.errorHandler = errorHandler;
const notFoundHandler = (req, res) => {
    res.status(404).json({
        success: false,
        error: {
            code: 'NOT_FOUND',
            message: `Route ${req.method} ${req.path} not found`
        }
    });
};
exports.notFoundHandler = notFoundHandler;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
//# sourceMappingURL=errorHandler.js.map