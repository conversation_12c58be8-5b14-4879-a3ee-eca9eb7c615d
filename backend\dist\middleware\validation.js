"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateRequestSize = exports.validateContentType = exports.handleValidationErrors = exports.validateParseRequest = void 0;
const express_validator_1 = require("express-validator");
const errors_1 = require("../utils/errors");
exports.validateParseRequest = [
    (0, express_validator_1.body)('content')
        .isString()
        .withMessage('Content must be a string')
        .isLength({ min: 1 })
        .withMessage('Content cannot be empty')
        .isLength({ max: 10485760 })
        .withMessage('Content exceeds maximum size limit'),
    (0, express_validator_1.body)('options')
        .optional()
        .isObject()
        .withMessage('Options must be an object'),
    (0, express_validator_1.body)('options.maxDepth')
        .optional()
        .isInt({ min: 1, max: 10 })
        .withMessage('maxDepth must be an integer between 1 and 10'),
    (0, express_validator_1.body)('options.includeContent')
        .optional()
        .isBoolean()
        .withMessage('includeContent must be a boolean'),
    (0, express_validator_1.body)('options.preserveWhitespace')
        .optional()
        .isBoolean()
        .withMessage('preserveWhitespace must be a boolean'),
    (0, express_validator_1.body)('options.extractMetadata')
        .optional()
        .isBoolean()
        .withMessage('extractMetadata must be a boolean')
];
const handleValidationErrors = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        const validationErrors = errors.array().map(error => ({
            field: error.type === 'field' ? error.path : 'unknown',
            message: error.msg,
            value: error.type === 'field' ? error.value : undefined
        }));
        throw new errors_1.ValidationError('Request validation failed', validationErrors);
    }
    next();
};
exports.handleValidationErrors = handleValidationErrors;
const validateContentType = (expectedTypes) => {
    return (req, res, next) => {
        const contentType = req.get('Content-Type');
        if (!contentType) {
            throw new errors_1.ValidationError('Content-Type header is required');
        }
        const isValidType = expectedTypes.some(type => contentType.toLowerCase().includes(type.toLowerCase()));
        if (!isValidType) {
            throw new errors_1.ValidationError(`Invalid Content-Type. Expected one of: ${expectedTypes.join(', ')}`);
        }
        next();
    };
};
exports.validateContentType = validateContentType;
const validateRequestSize = (maxSize) => {
    return (req, res, next) => {
        const contentLength = req.get('Content-Length');
        if (contentLength && parseInt(contentLength) > maxSize) {
            throw new errors_1.ValidationError(`Request size exceeds maximum limit of ${maxSize} bytes`);
        }
        next();
    };
};
exports.validateRequestSize = validateRequestSize;
//# sourceMappingURL=validation.js.map