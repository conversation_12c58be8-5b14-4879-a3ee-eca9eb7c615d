"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const multer_1 = __importDefault(require("multer"));
const parser_service_1 = require("../services/parser.service");
const fileValidation_1 = require("../utils/fileValidation");
const errors_1 = require("../utils/errors");
const errorHandler_1 = require("../middleware/errorHandler");
const validation_1 = require("../middleware/validation");
const logger_1 = require("../utils/logger");
const router = (0, express_1.Router)();
const upload = (0, multer_1.default)({
    storage: multer_1.default.memoryStorage(),
    limits: {
        fileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'),
        files: parseInt(process.env.MAX_FILES || '1')
    },
    fileFilter: (req, file, cb) => {
        const validation = (0, fileValidation_1.validateFile)(file);
        if (!validation.isValid) {
            cb(new errors_1.FileError(validation.error || 'Invalid file'));
            return;
        }
        cb(null, true);
    }
});
router.post('/', validation_1.validateParseRequest, validation_1.handleValidationErrors, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { content, options } = req.body;
    logger_1.logger.info('Parse request received', {
        contentLength: content.length,
        options,
        ip: req.ip
    });
    const contentValidation = (0, fileValidation_1.validateContent)(content);
    if (!contentValidation.isValid) {
        throw new errors_1.ValidationError(contentValidation.error || 'Invalid content');
    }
    try {
        const result = await parser_service_1.parserService.parseDocument(content, options);
        const response = {
            success: true,
            data: result
        };
        logger_1.logger.info('Parse request completed successfully', {
            nodeCount: result.statistics.nodeCount,
            maxDepth: result.statistics.maxDepth,
            processingTime: Date.now()
        });
        res.json(response);
    }
    catch (error) {
        logger_1.logger.error('Parse request failed', { error });
        throw new errors_1.ParseError(error instanceof Error ? error.message : 'Failed to parse document');
    }
}));
router.post('/file', upload.single('file'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.file) {
        throw new errors_1.ValidationError('No file uploaded');
    }
    const { file } = req;
    const options = req.body.options ? JSON.parse(req.body.options) : {};
    logger_1.logger.info('File parse request received', {
        filename: file.originalname,
        size: file.size,
        mimetype: file.mimetype,
        options,
        ip: req.ip
    });
    const fileValidation = (0, fileValidation_1.validateFile)(file);
    if (!fileValidation.isValid) {
        throw new errors_1.FileError(fileValidation.error || 'Invalid file');
    }
    try {
        const content = file.buffer.toString('utf8');
        const contentValidation = (0, fileValidation_1.validateContent)(content);
        if (!contentValidation.isValid) {
            throw new errors_1.ValidationError(contentValidation.error || 'Invalid file content');
        }
        const result = await parser_service_1.parserService.parseDocument(content, options);
        const response = {
            success: true,
            data: result
        };
        logger_1.logger.info('File parse request completed successfully', {
            filename: file.originalname,
            nodeCount: result.statistics.nodeCount,
            maxDepth: result.statistics.maxDepth
        });
        res.json(response);
    }
    catch (error) {
        logger_1.logger.error('File parse request failed', {
            error,
            filename: file.originalname
        });
        throw new errors_1.ParseError(error instanceof Error ? error.message : 'Failed to parse file');
    }
}));
router.get('/health', (req, res) => {
    const uptime = process.uptime();
    const memoryUsage = process.memoryUsage();
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || '1.0.0',
        uptime: Math.floor(uptime),
        memory: {
            used: Math.round(memoryUsage.heapUsed / 1024 / 1024),
            total: Math.round(memoryUsage.heapTotal / 1024 / 1024)
        }
    });
});
exports.default = router;
//# sourceMappingURL=parse.js.map