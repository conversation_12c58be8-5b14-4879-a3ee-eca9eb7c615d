import type { DocumentTree, ParseOptions, ParseResult, ValidationR<PERSON>ult, DocumentMetadata, DocumentStatistics, NodeMetadata } from '../types';
export declare class ParserService {
    private processor;
    constructor();
    parseDocument(input: string, options?: ParseOptions): Promise<ParseResult>;
    validateInput(input: string): ValidationResult;
    private validateHeadingStructure;
    preprocessMarkdown(input: string): string;
    buildTree(ast: any, options: Required<ParseOptions>): DocumentTree;
    private createNodeFromHeading;
    private extractHeadingText;
    private extractAssociatedContent;
    private nodeToMarkdown;
    private extractTextFromNode;
    private listToMarkdown;
    private insertNode;
    private generateNodeId;
    calculateNodeMetadata(content: string): NodeMetadata;
    private countWords;
    private calculateComplexity;
    extractMetadata(tree: DocumentTree): DocumentMetadata;
    calculateStatistics(tree: DocumentTree): DocumentStatistics;
    private countNodes;
    private calculateMaxDepth;
    private calculateBranchingFactor;
    private analyzeContentDistribution;
    private getAllNodes;
    private getTotalWordCount;
    private hasContentType;
    private getOverallComplexity;
    private createEmptyMetadata;
    private createEmptyNodeMetadata;
    private removeCircularReferences;
}
export declare const parserService: ParserService;
//# sourceMappingURL=parser.service.d.ts.map