"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.parserService = exports.ParserService = void 0;
const unified_1 = require("unified");
const remark_parse_1 = __importDefault(require("remark-parse"));
const remark_gfm_1 = __importDefault(require("remark-gfm"));
const remark_frontmatter_1 = __importDefault(require("remark-frontmatter"));
const remark_math_1 = __importDefault(require("remark-math"));
const unist_util_visit_1 = require("unist-util-visit");
const logger_1 = require("../utils/logger");
const MAX_INPUT_SIZE = 10 * 1024 * 1024;
const DEFAULT_PARSE_OPTIONS = {
    maxDepth: 6,
    includeContent: true,
    preserveWhitespace: false,
    extractMetadata: true,
    customPlugins: [],
};
class ParserService {
    constructor() {
        this.processor = (0, unified_1.unified)()
            .use(remark_parse_1.default)
            .use(remark_gfm_1.default)
            .use(remark_frontmatter_1.default)
            .use(remark_math_1.default);
        logger_1.logger.info('ParserService initialized');
    }
    async parseDocument(input, options = {}) {
        const startTime = Date.now();
        const opts = { ...DEFAULT_PARSE_OPTIONS, ...options };
        logger_1.logger.info('Starting document parsing', {
            inputLength: input.length,
            options: opts
        });
        try {
            const validation = this.validateInput(input);
            if (!validation.isValid) {
                const errorMessage = `Validation failed: ${validation.errors.map(e => e.message).join(', ')}`;
                logger_1.logger.error('Input validation failed', { errors: validation.errors });
                throw new Error(errorMessage);
            }
            const preprocessed = this.preprocessMarkdown(input);
            const ast = this.processor.parse(preprocessed);
            const processedAst = await this.processor.run(ast);
            const tree = this.buildTree(processedAst, opts);
            const metadata = opts.extractMetadata ? this.extractMetadata(tree) : this.createEmptyMetadata();
            const statistics = this.calculateStatistics(tree);
            const result = {
                tree: this.removeCircularReferences(tree),
                metadata,
                statistics,
                errors: [],
                warnings: validation.warnings,
            };
            const duration = Date.now() - startTime;
            logger_1.logger.info('Document parsing completed', {
                duration,
                nodeCount: statistics.nodeCount,
                maxDepth: statistics.maxDepth
            });
            return result;
        }
        catch (error) {
            const duration = Date.now() - startTime;
            logger_1.logger.error('Document parsing failed', { error, duration });
            throw new Error(`Parse failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    validateInput(input) {
        const errors = [];
        const warnings = [];
        if (input.length > MAX_INPUT_SIZE) {
            errors.push({
                type: 'SIZE_EXCEEDED',
                message: `Input size exceeds maximum limit of ${MAX_INPUT_SIZE} characters`,
            });
        }
        if (!input.trim()) {
            warnings.push({
                type: 'EMPTY_INPUT',
                message: 'Input is empty or contains only whitespace',
            });
        }
        const headingWarnings = this.validateHeadingStructure(input);
        warnings.push(...headingWarnings);
        return {
            isValid: errors.length === 0,
            errors,
            warnings,
        };
    }
    validateHeadingStructure(input) {
        const warnings = [];
        const lines = input.split('\n');
        const headings = [];
        lines.forEach((line, index) => {
            const match = line.match(/^(#{1,6})\s+(.+)/);
            if (match && match[1]) {
                headings.push({
                    level: match[1].length,
                    line: index + 1,
                });
            }
        });
        for (let i = 1; i < headings.length; i++) {
            const currentHeading = headings[i];
            const previousHeading = headings[i - 1];
            if (currentHeading && previousHeading) {
                const currentLevel = currentHeading.level;
                const previousLevel = previousHeading.level;
                if (currentLevel - previousLevel > 1) {
                    warnings.push({
                        type: 'SKIPPED_HEADING_LEVEL',
                        message: `Heading level skipped from H${previousLevel} to H${currentLevel}`,
                        line: currentHeading.line,
                    });
                }
            }
        }
        return warnings;
    }
    preprocessMarkdown(input) {
        let processed = input.replace(/^\uFEFF/, '');
        processed = processed.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
        if (!processed.endsWith('\n')) {
            processed += '\n';
        }
        return processed;
    }
    buildTree(ast, options) {
        const root = {
            id: 'root',
            level: 0,
            title: 'Document Root',
            content: '',
            rawContent: '',
            metadata: this.createEmptyNodeMetadata(),
            children: [],
        };
        const stack = [root];
        const headings = [];
        (0, unist_util_visit_1.visit)(ast, 'heading', (node) => {
            if (node.depth <= options.maxDepth) {
                headings.push(node);
            }
        });
        headings.forEach((heading, index) => {
            const node = this.createNodeFromHeading(heading, ast, index, headings, options);
            this.insertNode(stack, node);
        });
        return root;
    }
    createNodeFromHeading(heading, ast, index, allHeadings, options) {
        const title = this.extractHeadingText(heading);
        const content = options.includeContent ? this.extractAssociatedContent(ast, heading, allHeadings, index) : '';
        const node = {
            id: this.generateNodeId(title, heading.depth),
            level: heading.depth,
            title,
            content,
            rawContent: content,
            metadata: this.calculateNodeMetadata(content),
            children: [],
        };
        return node;
    }
    extractHeadingText(heading) {
        let text = '';
        (0, unist_util_visit_1.visit)(heading, 'text', (node) => {
            text += node.value;
        });
        return text.trim() || 'Untitled';
    }
    extractAssociatedContent(ast, heading, allHeadings, headingIndex) {
        const content = [];
        let collecting = false;
        const nextHeading = allHeadings[headingIndex + 1];
        const currentLevel = heading.depth;
        (0, unist_util_visit_1.visit)(ast, (node) => {
            if (node === heading) {
                collecting = true;
                return;
            }
            if (collecting) {
                if (node.type === 'heading' && node.depth <= currentLevel) {
                    collecting = false;
                    return;
                }
                if (nextHeading && node === nextHeading) {
                    collecting = false;
                    return;
                }
                if (node.type === 'paragraph' || node.type === 'list' || node.type === 'code' || node.type === 'blockquote') {
                    content.push(this.nodeToMarkdown(node));
                }
            }
        });
        return content.join('\n\n').trim();
    }
    nodeToMarkdown(node) {
        switch (node.type) {
            case 'paragraph':
                return this.extractTextFromNode(node);
            case 'list':
                return this.listToMarkdown(node);
            case 'code':
                return `\`\`\`${node.lang || ''}\n${node.value}\n\`\`\``;
            case 'blockquote':
                return `> ${this.extractTextFromNode(node)}`;
            default:
                return this.extractTextFromNode(node);
        }
    }
    extractTextFromNode(node) {
        let text = '';
        (0, unist_util_visit_1.visit)(node, 'text', (textNode) => {
            text += textNode.value;
        });
        return text;
    }
    listToMarkdown(node) {
        const items = [];
        if (node.children) {
            node.children.forEach((item, index) => {
                const text = this.extractTextFromNode(item);
                const prefix = node.ordered ? `${index + 1}. ` : '- ';
                items.push(prefix + text);
            });
        }
        return items.join('\n');
    }
    insertNode(stack, node) {
        while (stack.length > 1) {
            const lastNode = stack[stack.length - 1];
            if (lastNode && lastNode.level >= node.level) {
                stack.pop();
            }
            else {
                break;
            }
        }
        const parent = stack[stack.length - 1];
        if (parent) {
            node.parent = parent;
            parent.children.push(node);
        }
        stack.push(node);
    }
    generateNodeId(title, level) {
        const slug = title
            .toLowerCase()
            .replace(/[^\w\s-]/g, '')
            .replace(/\s+/g, '-')
            .substring(0, 50);
        return `${level}-${slug}-${Math.random().toString(36).substr(2, 9)}`;
    }
    calculateNodeMetadata(content) {
        const wordCount = this.countWords(content);
        const characterCount = content.length;
        return {
            wordCount,
            characterCount,
            hasImages: /!\[.*?\]\(.*?\)/.test(content),
            hasLinks: /\[.*?\]\(.*?\)/.test(content),
            hasCode: /```[\s\S]*?```|`[^`]+`/.test(content),
            hasTables: /\|.*\|/.test(content),
            hasLists: /^[\s]*[-*+]\s|^[\s]*\d+\.\s/m.test(content),
            complexity: this.calculateComplexity(content),
            estimatedReadTime: Math.ceil(wordCount / 200),
        };
    }
    countWords(content) {
        return content
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 0)
            .length;
    }
    calculateComplexity(content) {
        const wordCount = this.countWords(content);
        const hasComplexElements = /```[\s\S]*?```|\|.*\|/.test(content);
        if (wordCount < 50 && !hasComplexElements)
            return 'low';
        if (wordCount < 200 && !hasComplexElements)
            return 'medium';
        return 'high';
    }
    extractMetadata(tree) {
        const stats = this.calculateStatistics(tree);
        return {
            title: tree.children[0]?.title || 'Untitled Document',
            totalNodes: stats.nodeCount,
            maxDepth: stats.maxDepth,
            totalWordCount: this.getTotalWordCount(tree),
            estimatedReadTime: Math.ceil(this.getTotalWordCount(tree) / 200),
            hasImages: this.hasContentType(tree, 'hasImages'),
            hasCode: this.hasContentType(tree, 'hasCode'),
            hasTables: this.hasContentType(tree, 'hasTables'),
            complexity: this.getOverallComplexity(tree),
        };
    }
    calculateStatistics(tree) {
        const nodeCount = this.countNodes(tree);
        const maxDepth = this.calculateMaxDepth(tree);
        const avgBranchingFactor = this.calculateBranchingFactor(tree);
        const contentDistribution = this.analyzeContentDistribution(tree);
        return {
            nodeCount,
            maxDepth,
            avgBranchingFactor,
            contentDistribution,
        };
    }
    countNodes(tree) {
        let count = tree.level > 0 ? 1 : 0;
        for (const child of tree.children) {
            count += this.countNodes(child);
        }
        return count;
    }
    calculateMaxDepth(tree) {
        let maxDepth = tree.level;
        for (const child of tree.children) {
            maxDepth = Math.max(maxDepth, this.calculateMaxDepth(child));
        }
        return maxDepth;
    }
    calculateBranchingFactor(tree) {
        const nodes = this.getAllNodes(tree);
        const totalChildren = nodes.reduce((sum, node) => sum + node.children.length, 0);
        return nodes.length > 0 ? totalChildren / nodes.length : 0;
    }
    analyzeContentDistribution(tree) {
        const byLevel = {};
        const byComplexity = { low: 0, medium: 0, high: 0 };
        const byContentType = {};
        const traverse = (node) => {
            if (node.level > 0) {
                byLevel[node.level] = (byLevel[node.level] || 0) + 1;
                const complexity = node.metadata.complexity;
                if (complexity && complexity in byComplexity) {
                    byComplexity[complexity] = (byComplexity[complexity] || 0) + 1;
                }
                if (node.metadata.hasCode)
                    byContentType.code = (byContentType.code || 0) + 1;
                if (node.metadata.hasImages)
                    byContentType.images = (byContentType.images || 0) + 1;
                if (node.metadata.hasTables)
                    byContentType.tables = (byContentType.tables || 0) + 1;
                if (node.metadata.hasLists)
                    byContentType.lists = (byContentType.lists || 0) + 1;
            }
            node.children.forEach(traverse);
        };
        traverse(tree);
        return { byLevel, byComplexity, byContentType };
    }
    getAllNodes(tree) {
        const nodes = [];
        if (tree.level > 0)
            nodes.push(tree);
        for (const child of tree.children) {
            nodes.push(...this.getAllNodes(child));
        }
        return nodes;
    }
    getTotalWordCount(tree) {
        let total = tree.metadata.wordCount;
        for (const child of tree.children) {
            total += this.getTotalWordCount(child);
        }
        return total;
    }
    hasContentType(tree, type) {
        if (tree.metadata[type])
            return true;
        return tree.children.some(child => this.hasContentType(child, type));
    }
    getOverallComplexity(tree) {
        const nodes = this.getAllNodes(tree);
        const complexityScores = { low: 1, medium: 2, high: 3 };
        const avgScore = nodes.reduce((sum, node) => sum + complexityScores[node.metadata.complexity], 0) / nodes.length;
        if (avgScore < 1.5)
            return 'low';
        if (avgScore < 2.5)
            return 'medium';
        return 'high';
    }
    createEmptyMetadata() {
        return {
            title: 'Empty Document',
            totalNodes: 0,
            maxDepth: 0,
            totalWordCount: 0,
            estimatedReadTime: 0,
            hasImages: false,
            hasCode: false,
            hasTables: false,
            complexity: 'low',
        };
    }
    createEmptyNodeMetadata() {
        return {
            wordCount: 0,
            characterCount: 0,
            hasImages: false,
            hasLinks: false,
            hasCode: false,
            hasTables: false,
            hasLists: false,
            complexity: 'low',
            estimatedReadTime: 0,
        };
    }
    removeCircularReferences(tree) {
        const cleanTree = (node) => {
            const cleanedNode = {
                id: node.id,
                level: node.level,
                title: node.title,
                content: node.content,
                rawContent: node.rawContent,
                metadata: node.metadata,
                children: node.children.map(child => cleanTree(child)),
            };
            return cleanedNode;
        };
        return cleanTree(tree);
    }
}
exports.ParserService = ParserService;
exports.parserService = new ParserService();
//# sourceMappingURL=parser.service.js.map