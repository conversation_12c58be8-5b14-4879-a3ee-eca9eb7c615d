import type { ApiError } from '../types';
export declare class AppError extends <PERSON>rror implements ApiError {
    readonly code: string;
    readonly statusCode: number;
    readonly details?: any;
    constructor(message: string, code: string, statusCode?: number, details?: any);
}
export declare class ValidationError extends AppError {
    constructor(message: string, details?: any);
}
export declare class ParseError extends AppError {
    constructor(message: string, details?: any);
}
export declare class FileError extends AppError {
    constructor(message: string, details?: any);
}
export declare class RateLimitError extends AppError {
    constructor(message?: string);
}
export declare const formatErrorResponse: (error: Error | AppError) => {
    success: boolean;
    error: {
        code: string;
        message: string;
        details: any;
    };
};
//# sourceMappingURL=errors.d.ts.map