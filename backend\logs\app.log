{"level":"info","message":"ParserService initialized","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:02:10"}
{"environment":"development","level":"info","message":"Server running on port 3001","port":"3001","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:02:10"}
{"level":"info","message":"ParserService initialized","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:03:07"}
{"environment":"development","level":"info","message":"Server running on port 3001","port":"3001","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:03:07"}
{"level":"info","message":"ParserService initialized","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:04:32"}
{"environment":"development","level":"info","message":"Server running on port 3001","port":"3001","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:04:32"}
{"level":"info","message":"ParserService initialized","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:12:01"}
{"environment":"development","level":"info","message":"Server running on port 3001","port":"3001","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:12:01"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:12:26 +0000] \"GET /api/parse/health HTTP/1.1\" 200 116 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:12:26"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:12:56 +0000] \"GET /api/parse/health HTTP/1.1\" 200 116 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:12:56"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:13:26 +0000] \"GET /api/parse/health HTTP/1.1\" 200 116 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:13:26"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:13:56 +0000] \"GET /api/parse/health HTTP/1.1\" 200 117 \"-\" \"node\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:13:56"}
{"contentLength":589,"ip":"::1","level":"info","message":"Parse request received","options":{"extractMetadata":true,"includeContent":true,"maxDepth":6},"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:13:56"}
{"inputLength":589,"level":"info","message":"Starting document parsing","options":{"customPlugins":[],"extractMetadata":true,"includeContent":true,"maxDepth":6,"preserveWhitespace":false},"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:13:56"}
{"duration":21,"level":"info","maxDepth":3,"message":"Document parsing completed","nodeCount":7,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:13:56"}
{"level":"info","maxDepth":3,"message":"Parse request completed successfully","nodeCount":7,"processingTime":1749773636979,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:13:56"}
{"error":{},"level":"error","message":"Parse request failed","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:13:56"}
{"error":"Converting circular structure to JSON\n    --> starting at object with constructor 'Object'\n    |     property 'children' -> object with constructor 'Array'\n    |     index 0 -> object with constructor 'Object'\n    --- property 'parent' closes the circle","ip":"::1","level":"error","message":"Request error","method":"POST","service":"markdown-visualizer-api","stack":"ParseError: Converting circular structure to JSON\n    --> starting at object with constructor 'Object'\n    |     property 'children' -> object with constructor 'Array'\n    |     index 0 -> object with constructor 'Object'\n    --- property 'parent' closes the circle\n    at new ParseError (D:\\Project\\Markdown2Webpage\\backend\\src\\utils\\errors.ts:31:5)\n    at D:\\Project\\Markdown2Webpage\\backend\\src\\routes\\parse.ts:71:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-13 08:13:56","url":"/api/parse","userAgent":"node"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:13:56 +0000] \"POST /api/parse HTTP/1.1\" 422 318 \"-\" \"node\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:13:56"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:13:56 +0000] \"GET /api/parse/health HTTP/1.1\" 200 117 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:13:56"}
{"filename":"test.md","ip":"::1","level":"info","message":"File parse request received","mimetype":"text/markdown","options":{"extractMetadata":true,"includeContent":true,"maxDepth":6},"service":"markdown-visualizer-api","size":589,"timestamp":"2025-06-13 08:13:56"}
{"inputLength":589,"level":"info","message":"Starting document parsing","options":{"customPlugins":[],"extractMetadata":true,"includeContent":true,"maxDepth":6,"preserveWhitespace":false},"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:13:56"}
{"duration":7,"level":"info","maxDepth":3,"message":"Document parsing completed","nodeCount":7,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:13:57"}
{"filename":"test.md","level":"info","maxDepth":3,"message":"File parse request completed successfully","nodeCount":7,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:13:57"}
{"error":{},"filename":"test.md","level":"error","message":"File parse request failed","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:13:57"}
{"error":"Converting circular structure to JSON\n    --> starting at object with constructor 'Object'\n    |     property 'children' -> object with constructor 'Array'\n    |     index 0 -> object with constructor 'Object'\n    --- property 'parent' closes the circle","ip":"::1","level":"error","message":"Request error","method":"POST","service":"markdown-visualizer-api","stack":"ParseError: Converting circular structure to JSON\n    --> starting at object with constructor 'Object'\n    |     property 'children' -> object with constructor 'Array'\n    |     index 0 -> object with constructor 'Object'\n    --- property 'parent' closes the circle\n    at new ParseError (D:\\Project\\Markdown2Webpage\\backend\\src\\utils\\errors.ts:31:5)\n    at D:\\Project\\Markdown2Webpage\\backend\\src\\routes\\parse.ts:137:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-13 08:13:57","url":"/api/parse/file","userAgent":"node"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:13:57 +0000] \"POST /api/parse/file HTTP/1.1\" 422 318 \"-\" \"node\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:13:57"}
{"error":"Request validation failed","ip":"::1","level":"error","message":"Request error","method":"POST","service":"markdown-visualizer-api","stack":"ValidationError: Request validation failed\n    at new ValidationError (D:\\Project\\Markdown2Webpage\\backend\\src\\utils\\errors.ts:24:5)\n    at handleValidationErrors (D:\\Project\\Markdown2Webpage\\backend\\src\\middleware\\validation.ts:52:11)\n    at Layer.handle [as handle_request] (D:\\Project\\Markdown2Webpage\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\Project\\Markdown2Webpage\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\Project\\Markdown2Webpage\\backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-13 08:13:57","url":"/api/parse","userAgent":"node"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:13:57 +0000] \"POST /api/parse HTTP/1.1\" 400 170 \"-\" \"node\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:13:57"}
{"level":"info","message":"ParserService initialized","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:14:19"}
{"environment":"development","level":"info","message":"Server running on port 3001","port":"3001","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:14:19"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:14:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 116 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:14:29"}
{"level":"info","message":"ParserService initialized","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:14:35"}
{"environment":"development","level":"info","message":"Server running on port 3001","port":"3001","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:14:36"}
{"level":"info","message":"ParserService initialized","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:16:17"}
{"environment":"development","level":"info","message":"Server running on port 3001","port":"3001","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:16:17"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:16:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 116 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:16:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:17:05 +0000] \"GET /api/parse/health HTTP/1.1\" 200 116 \"-\" \"node\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:05"}
{"contentLength":589,"ip":"::1","level":"info","message":"Parse request received","options":{"extractMetadata":true,"includeContent":true,"maxDepth":6},"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:05"}
{"inputLength":589,"level":"info","message":"Starting document parsing","options":{"customPlugins":[],"extractMetadata":true,"includeContent":true,"maxDepth":6,"preserveWhitespace":false},"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:05"}
{"duration":22,"level":"info","maxDepth":3,"message":"Document parsing completed","nodeCount":7,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:05"}
{"level":"info","maxDepth":3,"message":"Parse request completed successfully","nodeCount":7,"processingTime":1749773825217,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:05"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:17:05 +0000] \"POST /api/parse HTTP/1.1\" 200 - \"-\" \"node\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:05"}
{"filename":"test.md","ip":"::1","level":"info","message":"File parse request received","mimetype":"text/markdown","options":{"extractMetadata":true,"includeContent":true,"maxDepth":6},"service":"markdown-visualizer-api","size":589,"timestamp":"2025-06-13 08:17:05"}
{"inputLength":589,"level":"info","message":"Starting document parsing","options":{"customPlugins":[],"extractMetadata":true,"includeContent":true,"maxDepth":6,"preserveWhitespace":false},"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:05"}
{"duration":7,"level":"info","maxDepth":3,"message":"Document parsing completed","nodeCount":7,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:05"}
{"filename":"test.md","level":"info","maxDepth":3,"message":"File parse request completed successfully","nodeCount":7,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:05"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:17:05 +0000] \"POST /api/parse/file HTTP/1.1\" 200 - \"-\" \"node\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:05"}
{"error":"Request validation failed","ip":"::1","level":"error","message":"Request error","method":"POST","service":"markdown-visualizer-api","stack":"ValidationError: Request validation failed\n    at new ValidationError (D:\\Project\\Markdown2Webpage\\backend\\src\\utils\\errors.ts:24:5)\n    at handleValidationErrors (D:\\Project\\Markdown2Webpage\\backend\\src\\middleware\\validation.ts:52:11)\n    at Layer.handle [as handle_request] (D:\\Project\\Markdown2Webpage\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\Project\\Markdown2Webpage\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\Project\\Markdown2Webpage\\backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-13 08:17:05","url":"/api/parse","userAgent":"node"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:17:05 +0000] \"POST /api/parse HTTP/1.1\" 400 170 \"-\" \"node\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:05"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:17:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 116 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:29"}
{"contentLength":4661,"ip":"::1","level":"info","message":"Parse request received","options":{"extractMetadata":true,"includeContent":true,"maxDepth":6},"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"inputLength":4661,"level":"info","message":"Starting document parsing","options":{"customPlugins":[],"extractMetadata":true,"includeContent":true,"maxDepth":6,"preserveWhitespace":false},"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"duration":41,"level":"info","maxDepth":4,"message":"Document parsing completed","nodeCount":32,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"level":"info","maxDepth":4,"message":"Parse request completed successfully","nodeCount":32,"processingTime":1749773873643,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:17:53 +0000] \"POST /api/parse HTTP/1.1\" 200 - \"-\" \"node\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"filename":"sample.md","ip":"::1","level":"info","message":"File parse request received","mimetype":"text/markdown","options":{"extractMetadata":true,"includeContent":true,"maxDepth":6},"service":"markdown-visualizer-api","size":4661,"timestamp":"2025-06-13 08:17:53"}
{"inputLength":4661,"level":"info","message":"Starting document parsing","options":{"customPlugins":[],"extractMetadata":true,"includeContent":true,"maxDepth":6,"preserveWhitespace":false},"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"duration":35,"level":"info","maxDepth":4,"message":"Document parsing completed","nodeCount":32,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"filename":"sample.md","level":"info","maxDepth":4,"message":"File parse request completed successfully","nodeCount":32,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:17:53 +0000] \"POST /api/parse/file HTTP/1.1\" 200 - \"-\" \"node\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"contentLength":23,"ip":"::1","level":"info","message":"Parse request received","options":{"extractMetadata":true,"includeContent":true,"maxDepth":6},"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"inputLength":23,"level":"info","message":"Starting document parsing","options":{"customPlugins":[],"extractMetadata":true,"includeContent":true,"maxDepth":6,"preserveWhitespace":false},"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"duration":1,"level":"info","maxDepth":3,"message":"Document parsing completed","nodeCount":3,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"level":"info","maxDepth":3,"message":"Parse request completed successfully","nodeCount":3,"processingTime":1749773873706,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:17:53 +0000] \"POST /api/parse HTTP/1.1\" 200 - \"-\" \"node\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"contentLength":69,"ip":"::1","level":"info","message":"Parse request received","options":{"extractMetadata":true,"includeContent":true,"maxDepth":6},"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"inputLength":69,"level":"info","message":"Starting document parsing","options":{"customPlugins":[],"extractMetadata":true,"includeContent":true,"maxDepth":6,"preserveWhitespace":false},"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"duration":2,"level":"info","maxDepth":1,"message":"Document parsing completed","nodeCount":1,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"level":"info","maxDepth":1,"message":"Parse request completed successfully","nodeCount":1,"processingTime":1749773873714,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:17:53 +0000] \"POST /api/parse HTTP/1.1\" 200 - \"-\" \"node\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"contentLength":222,"ip":"::1","level":"info","message":"Parse request received","options":{"extractMetadata":true,"includeContent":true,"maxDepth":6},"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"inputLength":222,"level":"info","message":"Starting document parsing","options":{"customPlugins":[],"extractMetadata":true,"includeContent":true,"maxDepth":6,"preserveWhitespace":false},"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"duration":1,"level":"info","maxDepth":1,"message":"Document parsing completed","nodeCount":1,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"level":"info","maxDepth":1,"message":"Parse request completed successfully","nodeCount":1,"processingTime":1749773873720,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:17:53 +0000] \"POST /api/parse HTTP/1.1\" 200 - \"-\" \"node\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"contentLength":311,"ip":"::1","level":"info","message":"Parse request received","options":{"extractMetadata":true,"includeContent":true,"maxDepth":6},"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"inputLength":311,"level":"info","message":"Starting document parsing","options":{"customPlugins":[],"extractMetadata":true,"includeContent":true,"maxDepth":6,"preserveWhitespace":false},"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"duration":3,"level":"info","maxDepth":2,"message":"Document parsing completed","nodeCount":5,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"level":"info","maxDepth":2,"message":"Parse request completed successfully","nodeCount":5,"processingTime":1749773873729,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:17:53 +0000] \"POST /api/parse HTTP/1.1\" 200 - \"-\" \"node\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:17:53"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:18:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 117 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:18:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:19:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 117 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:19:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:19:38 +0000] \"GET /api/parse/health HTTP/1.1\" 200 117 \"-\" \"node\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:19:38"}
{"contentLength":30,"ip":"::1","level":"info","message":"Parse request received","options":{"extractMetadata":true,"includeContent":true,"maxDepth":6},"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:19:38"}
{"inputLength":30,"level":"info","message":"Starting document parsing","options":{"customPlugins":[],"extractMetadata":true,"includeContent":true,"maxDepth":6,"preserveWhitespace":false},"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:19:38"}
{"duration":2,"level":"info","maxDepth":2,"message":"Document parsing completed","nodeCount":2,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:19:38"}
{"level":"info","maxDepth":2,"message":"Parse request completed successfully","nodeCount":2,"processingTime":1749773978106,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:19:38"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:19:38 +0000] \"POST /api/parse HTTP/1.1\" 200 - \"-\" \"node\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:19:38"}
{"filename":"test.md","ip":"::1","level":"info","message":"File parse request received","mimetype":"text/markdown","options":{},"service":"markdown-visualizer-api","size":19,"timestamp":"2025-06-13 08:19:38"}
{"inputLength":19,"level":"info","message":"Starting document parsing","options":{"customPlugins":[],"extractMetadata":true,"includeContent":true,"maxDepth":6,"preserveWhitespace":false},"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:19:38"}
{"duration":3,"level":"info","maxDepth":1,"message":"Document parsing completed","nodeCount":1,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:19:38"}
{"filename":"test.md","level":"info","maxDepth":1,"message":"File parse request completed successfully","nodeCount":1,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:19:38"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:19:38 +0000] \"POST /api/parse/file HTTP/1.1\" 200 956 \"-\" \"node\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:19:38"}
{"error":"Request validation failed","ip":"::1","level":"error","message":"Request error","method":"POST","service":"markdown-visualizer-api","stack":"ValidationError: Request validation failed\n    at new ValidationError (D:\\Project\\Markdown2Webpage\\backend\\src\\utils\\errors.ts:24:5)\n    at handleValidationErrors (D:\\Project\\Markdown2Webpage\\backend\\src\\middleware\\validation.ts:52:11)\n    at Layer.handle [as handle_request] (D:\\Project\\Markdown2Webpage\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\Project\\Markdown2Webpage\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\Project\\Markdown2Webpage\\backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-13 08:19:38","url":"/api/parse","userAgent":"node"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:19:38 +0000] \"POST /api/parse HTTP/1.1\" 400 170 \"-\" \"node\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:19:38"}
{"contentLength":4661,"ip":"::1","level":"info","message":"Parse request received","options":{"extractMetadata":true,"includeContent":true,"maxDepth":6},"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:19:38"}
{"inputLength":4661,"level":"info","message":"Starting document parsing","options":{"customPlugins":[],"extractMetadata":true,"includeContent":true,"maxDepth":6,"preserveWhitespace":false},"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:19:38"}
{"duration":41,"level":"info","maxDepth":4,"message":"Document parsing completed","nodeCount":32,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:19:38"}
{"level":"info","maxDepth":4,"message":"Parse request completed successfully","nodeCount":32,"processingTime":1749773978176,"service":"markdown-visualizer-api","timestamp":"2025-06-13 08:19:38"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:19:38 +0000] \"POST /api/parse HTTP/1.1\" 200 - \"-\" \"node\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:19:38"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:20:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 117 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:20:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:21:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 117 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:21:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:22:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 117 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:22:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:23:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 117 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:23:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:24:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 117 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:24:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:25:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 117 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:25:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:26:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 117 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:26:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:27:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 117 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:27:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:28:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 117 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:28:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:29:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 117 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:29:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:30:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 117 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:30:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:31:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 117 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:31:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:32:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 117 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:32:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:33:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:33:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:34:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:34:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:35:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:35:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:36:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:36:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:37:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:37:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:38:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:38:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:39:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:39:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:40:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:40:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:41:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:41:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:42:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:42:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:43:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:43:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:44:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:44:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:45:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:45:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:46:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:46:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:46:56 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:46:56"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:47:26 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:47:26"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:47:56 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:47:56"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:48:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:48:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:49:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:49:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:50:08 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:50:08"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:50:29 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:50:29"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:51:23 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:51:23"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:51:23 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:51:23"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:51:35 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:51:35"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:51:35 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:51:35"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:52:06 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:52:06"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:52:36 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:52:36"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:53:06 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:53:06"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:53:36 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:53:36"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:53:37 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:53:37"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:53:37 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:53:37"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:53:37 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:53:37"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:53:37 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:53:37"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:54:08 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:54:08"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:54:08 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:54:08"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:54:25 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:54:25"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:54:37 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:54:37"}
{"level":"info","message":"::1 - - [13/Jun/2025:00:54:38 +0000] \"GET /api/parse/health HTTP/1.1\" 200 118 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:54:38"}
