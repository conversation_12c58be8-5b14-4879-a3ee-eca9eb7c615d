{"error":{},"level":"error","message":"Parse request failed","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:13:56"}
{"error":"Converting circular structure to JSON\n    --> starting at object with constructor 'Object'\n    |     property 'children' -> object with constructor 'Array'\n    |     index 0 -> object with constructor 'Object'\n    --- property 'parent' closes the circle","ip":"::1","level":"error","message":"Request error","method":"POST","service":"markdown-visualizer-api","stack":"ParseError: Converting circular structure to JSON\n    --> starting at object with constructor 'Object'\n    |     property 'children' -> object with constructor 'Array'\n    |     index 0 -> object with constructor 'Object'\n    --- property 'parent' closes the circle\n    at new ParseError (D:\\Project\\Markdown2Webpage\\backend\\src\\utils\\errors.ts:31:5)\n    at D:\\Project\\Markdown2Webpage\\backend\\src\\routes\\parse.ts:71:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-13 08:13:56","url":"/api/parse","userAgent":"node"}
{"error":{},"filename":"test.md","level":"error","message":"File parse request failed","service":"markdown-visualizer-api","timestamp":"2025-06-13 08:13:57"}
{"error":"Converting circular structure to JSON\n    --> starting at object with constructor 'Object'\n    |     property 'children' -> object with constructor 'Array'\n    |     index 0 -> object with constructor 'Object'\n    --- property 'parent' closes the circle","ip":"::1","level":"error","message":"Request error","method":"POST","service":"markdown-visualizer-api","stack":"ParseError: Converting circular structure to JSON\n    --> starting at object with constructor 'Object'\n    |     property 'children' -> object with constructor 'Array'\n    |     index 0 -> object with constructor 'Object'\n    --- property 'parent' closes the circle\n    at new ParseError (D:\\Project\\Markdown2Webpage\\backend\\src\\utils\\errors.ts:31:5)\n    at D:\\Project\\Markdown2Webpage\\backend\\src\\routes\\parse.ts:137:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-13 08:13:57","url":"/api/parse/file","userAgent":"node"}
{"error":"Request validation failed","ip":"::1","level":"error","message":"Request error","method":"POST","service":"markdown-visualizer-api","stack":"ValidationError: Request validation failed\n    at new ValidationError (D:\\Project\\Markdown2Webpage\\backend\\src\\utils\\errors.ts:24:5)\n    at handleValidationErrors (D:\\Project\\Markdown2Webpage\\backend\\src\\middleware\\validation.ts:52:11)\n    at Layer.handle [as handle_request] (D:\\Project\\Markdown2Webpage\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\Project\\Markdown2Webpage\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\Project\\Markdown2Webpage\\backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-13 08:13:57","url":"/api/parse","userAgent":"node"}
{"error":"Request validation failed","ip":"::1","level":"error","message":"Request error","method":"POST","service":"markdown-visualizer-api","stack":"ValidationError: Request validation failed\n    at new ValidationError (D:\\Project\\Markdown2Webpage\\backend\\src\\utils\\errors.ts:24:5)\n    at handleValidationErrors (D:\\Project\\Markdown2Webpage\\backend\\src\\middleware\\validation.ts:52:11)\n    at Layer.handle [as handle_request] (D:\\Project\\Markdown2Webpage\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\Project\\Markdown2Webpage\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\Project\\Markdown2Webpage\\backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-13 08:17:05","url":"/api/parse","userAgent":"node"}
{"error":"Request validation failed","ip":"::1","level":"error","message":"Request error","method":"POST","service":"markdown-visualizer-api","stack":"ValidationError: Request validation failed\n    at new ValidationError (D:\\Project\\Markdown2Webpage\\backend\\src\\utils\\errors.ts:24:5)\n    at handleValidationErrors (D:\\Project\\Markdown2Webpage\\backend\\src\\middleware\\validation.ts:52:11)\n    at Layer.handle [as handle_request] (D:\\Project\\Markdown2Webpage\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\Project\\Markdown2Webpage\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\Project\\Markdown2Webpage\\backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-13 08:19:38","url":"/api/parse","userAgent":"node"}
