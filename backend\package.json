{"name": "markdown-visualizer-api", "version": "1.0.0", "description": "Backend API service for Markdown Visualizer", "main": "dist/app.js", "scripts": {"dev": "tsx watch src/app.ts", "build": "tsc && tsc-alias", "start": "node dist/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit"}, "keywords": ["markdown", "parser", "api", "visualization"], "author": "Markdown Visualizer Team", "license": "MIT", "dependencies": {"compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "helmet": "^7.0.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "remark": "^15.0.1", "remark-frontmatter": "^5.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "remark-parse": "^11.0.0", "unified": "^11.0.3", "unist-util-visit": "^5.0.0", "winston": "^3.10.0"}, "devDependencies": {"@types/compression": "^1.7.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.4", "@types/morgan": "^1.9.4", "@types/multer": "^1.4.7", "@types/node": "^20.5.0", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "eslint": "^8.47.0", "jest": "^29.6.2", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "tsc-alias": "^1.8.16", "tsx": "^3.12.7", "typescript": "^5.1.6"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "extensionsToTreatAsEsm": [".ts"], "globals": {"ts-jest": {"useESM": true}}, "roots": ["<rootDir>/src", "<rootDir>/tests"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "moduleNameMapper": {"^@/(.*)$": "<rootDir>/src/$1", "^@types/(.*)$": "<rootDir>/src/types/$1", "^@services/(.*)$": "<rootDir>/src/services/$1", "^@routes/(.*)$": "<rootDir>/src/routes/$1", "^@middleware/(.*)$": "<rootDir>/src/middleware/$1", "^@utils/(.*)$": "<rootDir>/src/utils/$1"}, "transformIgnorePatterns": ["node_modules/(?!(unified|remark|unist|vfile|micromark|decode-named-character-reference|character-entities|mdast|remark-parse|remark-gfm|remark-frontmatter|remark-math|unist-util-visit)/)"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/app.ts"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}}