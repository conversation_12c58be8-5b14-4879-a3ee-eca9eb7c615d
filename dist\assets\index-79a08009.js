var Ot=Object.defineProperty;var $t=(e,t,r)=>t in e?Ot(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var D=(e,t,r)=>($t(e,typeof t!="symbol"?t+"":t,r),r);import{r as m,a as Pt,g as Ce,R as se,c as X}from"./vendor-27d6d7f2.js";import{c as Bt,v as Vt,o as F,a as Re,u as U,b as Se,n as Y,h as at,d as Le,e as G,m as M,f as Ut,g as I,i as Wt,s as ce,r as Ht,j as ze,k as T,l as R,p as Zt,q as qt,t as Gt,w as Kt,x as Q}from"./markdown-51287e2f.js";import{s as ue,z as Yt,h as _e,t as de,i as Jt,l as Xt,a as Qt,b as er,c as tr,d as rr}from"./d3-b3606c65.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))n(a);new MutationObserver(a=>{for(const s of a)if(s.type==="childList")for(const o of s.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&n(o)}).observe(document,{childList:!0,subtree:!0});function r(a){const s={};return a.integrity&&(s.integrity=a.integrity),a.referrerPolicy&&(s.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?s.credentials="include":a.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function n(a){if(a.ep)return;a.ep=!0;const s=r(a);fetch(a.href,s)}})();var it={exports:{}},oe={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var nr=m,ar=Symbol.for("react.element"),ir=Symbol.for("react.fragment"),sr=Object.prototype.hasOwnProperty,or=nr.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,lr={key:!0,ref:!0,__self:!0,__source:!0};function st(e,t,r){var n,a={},s=null,o=null;r!==void 0&&(s=""+r),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(o=t.ref);for(n in t)sr.call(t,n)&&!lr.hasOwnProperty(n)&&(a[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps,t)a[n]===void 0&&(a[n]=t[n]);return{$$typeof:ar,type:e,key:s,ref:o,props:a,_owner:or.current}}oe.Fragment=ir;oe.jsx=st;oe.jsxs=st;it.exports=oe;var i=it.exports,be={},Oe=Pt;be.createRoot=Oe.createRoot,be.hydrateRoot=Oe.hydrateRoot;const $e=e=>{let t;const r=new Set,n=(h,l)=>{const b=typeof h=="function"?h(t):h;if(!Object.is(b,t)){const f=t;t=l??(typeof b!="object"||b===null)?b:Object.assign({},t,b),r.forEach(g=>g(t,f))}},a=()=>t,u={setState:n,getState:a,getInitialState:()=>d,subscribe:h=>(r.add(h),()=>r.delete(h)),destroy:()=>{r.clear()}},d=t=e(n,a,u);return u},cr=e=>e?$e(e):$e;var ot={exports:{}},lt={},ct={exports:{}},ut={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var W=m;function ur(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var dr=typeof Object.is=="function"?Object.is:ur,hr=W.useState,mr=W.useEffect,fr=W.useLayoutEffect,gr=W.useDebugValue;function xr(e,t){var r=t(),n=hr({inst:{value:r,getSnapshot:t}}),a=n[0].inst,s=n[1];return fr(function(){a.value=r,a.getSnapshot=t,he(a)&&s({inst:a})},[e,r,t]),mr(function(){return he(a)&&s({inst:a}),e(function(){he(a)&&s({inst:a})})},[e]),gr(r),r}function he(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!dr(e,r)}catch{return!0}}function pr(e,t){return t()}var yr=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?pr:xr;ut.useSyncExternalStore=W.useSyncExternalStore!==void 0?W.useSyncExternalStore:yr;ct.exports=ut;var br=ct.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var le=m,wr=br;function kr(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var vr=typeof Object.is=="function"?Object.is:kr,jr=wr.useSyncExternalStore,Nr=le.useRef,Er=le.useEffect,Cr=le.useMemo,Sr=le.useDebugValue;lt.useSyncExternalStoreWithSelector=function(e,t,r,n,a){var s=Nr(null);if(s.current===null){var o={hasValue:!1,value:null};s.current=o}else o=s.current;s=Cr(function(){function u(f){if(!d){if(d=!0,h=f,f=n(f),a!==void 0&&o.hasValue){var g=o.value;if(a(g,f))return l=g}return l=f}if(g=l,vr(h,f))return g;var y=n(f);return a!==void 0&&a(g,y)?(h=f,g):(h=f,l=y)}var d=!1,h,l,b=r===void 0?null:r;return[function(){return u(t())},b===null?void 0:function(){return u(b())}]},[t,r,n,a]);var c=jr(e,s[0],s[1]);return Er(function(){o.hasValue=!0,o.value=c},[c]),Sr(c),c};ot.exports=lt;var Lr=ot.exports;const Ar=Ce(Lr),{useDebugValue:Tr}=se,{useSyncExternalStoreWithSelector:Mr}=Ar;const Dr=e=>e;function Ir(e,t=Dr,r){const n=Mr(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,r);return Tr(n),n}const Pe=e=>{const t=typeof e=="function"?cr(e):e,r=(n,a)=>Ir(t,n,a);return Object.assign(r,t),r},Fr=e=>e?Pe(e):Pe,we=new Map,ee=e=>{const t=we.get(e);return t?Object.fromEntries(Object.entries(t.stores).map(([r,n])=>[r,n.getState()])):{}},Rr=(e,t,r)=>{if(e===void 0)return{type:"untracked",connection:t.connect(r)};const n=we.get(r.name);if(n)return{type:"tracked",store:e,...n};const a={connection:t.connect(r),stores:{}};return we.set(r.name,a),{type:"tracked",store:e,...a}},zr=(e,t={})=>(r,n,a)=>{const{enabled:s,anonymousActionType:o,store:c,...u}=t;let d;try{d=(s??!1)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!d)return e(r,n,a);const{connection:h,...l}=Rr(c,d,u);let b=!0;a.setState=(y,v,x)=>{const p=r(y,v);if(!b)return p;const k=x===void 0?{type:o||"anonymous"}:typeof x=="string"?{type:x}:x;return c===void 0?(h?.send(k,n()),p):(h?.send({...k,type:`${c}/${k.type}`},{...ee(u.name),[c]:a.getState()}),p)};const f=(...y)=>{const v=b;b=!1,r(...y),b=v},g=e(a.setState,n,a);if(l.type==="untracked"?h?.init(g):(l.stores[l.store]=a,h?.init(Object.fromEntries(Object.entries(l.stores).map(([y,v])=>[y,y===l.store?g:v.getState()])))),a.dispatchFromDevtools&&typeof a.dispatch=="function"){let y=!1;const v=a.dispatch;a.dispatch=(...x)=>{v(...x)}}return h.subscribe(y=>{var v;switch(y.type){case"ACTION":if(typeof y.payload!="string"){console.error("[zustand devtools middleware] Unsupported action format");return}return me(y.payload,x=>{if(x.type==="__setState"){if(c===void 0){f(x.state);return}Object.keys(x.state).length!==1&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format. 
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);const p=x.state[c];if(p==null)return;JSON.stringify(a.getState())!==JSON.stringify(p)&&f(p);return}a.dispatchFromDevtools&&typeof a.dispatch=="function"&&a.dispatch(x)});case"DISPATCH":switch(y.payload.type){case"RESET":return f(g),c===void 0?h?.init(a.getState()):h?.init(ee(u.name));case"COMMIT":if(c===void 0){h?.init(a.getState());return}return h?.init(ee(u.name));case"ROLLBACK":return me(y.state,x=>{if(c===void 0){f(x),h?.init(a.getState());return}f(x[c]),h?.init(ee(u.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return me(y.state,x=>{if(c===void 0){f(x);return}JSON.stringify(a.getState())!==JSON.stringify(x[c])&&f(x[c])});case"IMPORT_STATE":{const{nextLiftedState:x}=y.payload,p=(v=x.computedStates.slice(-1)[0])==null?void 0:v.state;if(!p)return;f(c===void 0?p:p[c]),h?.send(null,x);return}case"PAUSE_RECORDING":return b=!b}return}}),g},_r=zr,me=(e,t)=>{let r;try{r=JSON.parse(e)}catch(n){console.error("[zustand devtools middleware] Could not parse the received json",n)}r!==void 0&&t(r)},Or={layout:"tree",orientation:"vertical",width:800,height:600,padding:{top:20,right:20,bottom:20,left:20},nodeSize:{baseSize:12,sizeByContent:!0,minSize:8,maxSize:24,sizeMetric:"wordCount"},nodeColors:{primary:"#3b82f6",secondary:"#64748b",accent:"#d946ef",background:"#ffffff",text:"#1f2937",levels:["#3b82f6","#10b981","#f59e0b","#ef4444","#8b5cf6","#06b6d4"],states:{hover:"#60a5fa",selected:"#1d4ed8",visited:"#9ca3af"}},nodeSpacing:30,enableZoom:!0,enablePan:!0,enableHover:!0,animationDuration:300,animationEasing:"cubic-bezier(0.4, 0, 0.2, 1)",responsive:!0,breakpoints:[{name:"mobile",minWidth:0,config:{nodeSize:{baseSize:8,minSize:6,maxSize:16,sizeByContent:!0,sizeMetric:"wordCount"}}},{name:"tablet",minWidth:768,config:{nodeSize:{baseSize:10,minSize:8,maxSize:20,sizeByContent:!0,sizeMetric:"wordCount"}}},{name:"desktop",minWidth:1024,config:{nodeSize:{baseSize:12,minSize:8,maxSize:24,sizeByContent:!0,sizeMetric:"wordCount"}}}]},Be={isOpen:!1,currentNode:null,previousNode:null,navigationHistory:[],breadcrumbs:[],scrollPosition:0,isLoading:!1,error:null},$r={levels:[1,2,3,4,5,6],complexity:["low","medium","high"],contentTypes:["text","code","images","tables","lists"],showEmpty:!0},L=Fr()(_r((e,t)=>({document:{raw:"",tree:null,metadata:null,isLoading:!1,error:null},visualization:{type:"tree",config:Or,selectedNode:null,hoveredNode:null,zoomLevel:1},modal:Be,ui:{sidebarOpen:!0,searchQuery:"",searchResults:[],filters:$r,theme:"light",isFullscreen:!1},setDocument:(r,n,a)=>e(s=>({document:{...s.document,raw:r,tree:n,metadata:a,isLoading:!1,error:null}}),!1,"setDocument"),clearDocument:()=>e(r=>({document:{...r.document,raw:"",tree:null,metadata:null,error:null},modal:Be,visualization:{...r.visualization,selectedNode:null,hoveredNode:null}}),!1,"clearDocument"),setDocumentLoading:r=>e(n=>({document:{...n.document,isLoading:r}}),!1,"setDocumentLoading"),setDocumentError:r=>e(n=>({document:{...n.document,error:r,isLoading:!1}}),!1,"setDocumentError"),setVisualizationType:r=>e(n=>({visualization:{...n.visualization,type:r}}),!1,"setVisualizationType"),updateVisualizationConfig:r=>e(n=>({visualization:{...n.visualization,config:{...n.visualization.config,...r}}}),!1,"updateVisualizationConfig"),setSelectedNode:r=>e(n=>({visualization:{...n.visualization,selectedNode:r}}),!1,"setSelectedNode"),setHoveredNode:r=>e(n=>({visualization:{...n.visualization,hoveredNode:r}}),!1,"setHoveredNode"),setZoomLevel:r=>e(n=>({visualization:{...n.visualization,zoomLevel:r}}),!1,"setZoomLevel"),openModal:r=>{const{document:n}=t();if(!n.tree)return;const a=(u,d)=>{if(u.id===d)return u;for(const h of u.children){const l=a(h,d);if(l)return l}return null},s=a(n.tree,r);if(!s)return;const o=[];let c=s;for(;c;)o.unshift({id:c.id,title:c.title,level:c.level,isActive:c.id===r}),c=c.parent;e(u=>({modal:{...u.modal,isOpen:!0,currentNode:s,breadcrumbs:o,isLoading:!1,error:null}}),!1,"openModal")},closeModal:()=>e(r=>({modal:{...r.modal,isOpen:!1,currentNode:null}}),!1,"closeModal"),setModalLoading:r=>e(n=>({modal:{...n.modal,isLoading:r}}),!1,"setModalLoading"),setModalError:r=>e(n=>({modal:{...n.modal,error:r,isLoading:!1}}),!1,"setModalError"),addToModalHistory:r=>e(n=>({modal:{...n.modal,navigationHistory:[...n.modal.navigationHistory,{nodeId:r,timestamp:Date.now(),scrollPosition:0,source:"programmatic"}]}}),!1,"addToModalHistory"),toggleSidebar:()=>e(r=>({ui:{...r.ui,sidebarOpen:!r.ui.sidebarOpen}}),!1,"toggleSidebar"),setSearchQuery:r=>e(n=>({ui:{...n.ui,searchQuery:r}}),!1,"setSearchQuery"),setSearchResults:r=>e(n=>({ui:{...n.ui,searchResults:r}}),!1,"setSearchResults"),updateFilters:r=>e(n=>({ui:{...n.ui,filters:{...n.ui.filters,...r}}}),!1,"updateFilters"),setTheme:r=>e(n=>({ui:{...n.ui,theme:r}}),!1,"setTheme"),toggleFullscreen:()=>e(r=>({ui:{...r.ui,isFullscreen:!r.ui.isFullscreen}}),!1,"toggleFullscreen")}),{name:"markdown-visualizer-store"}));function Pr(e){if(typeof e!="string")throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}function Br(e,t,r){const a=Bt((r||{}).ignore||[]),s=Vr(t);let o=-1;for(;++o<s.length;)Vt(e,"text",c);function c(d,h){let l=-1,b;for(;++l<h.length;){const f=h[l],g=b?b.children:void 0;if(a(f,g?g.indexOf(f):void 0,b))return;b=f}if(b)return u(d,h)}function u(d,h){const l=h[h.length-1],b=s[o][0],f=s[o][1];let g=0;const v=l.children.indexOf(d);let x=!1,p=[];b.lastIndex=0;let k=b.exec(d.value);for(;k;){const C=k.index,E={index:k.index,input:k.input,stack:[...h,d]};let j=f(...k,E);if(typeof j=="string"&&(j=j.length>0?{type:"text",value:j}:void 0),j===!1?b.lastIndex=C+1:(g!==C&&p.push({type:"text",value:d.value.slice(g,C)}),Array.isArray(j)?p.push(...j):j&&p.push(j),g=C+k[0].length,x=!0),!b.global)break;k=b.exec(d.value)}return x?(g<d.value.length&&p.push({type:"text",value:d.value.slice(g)}),l.children.splice(v,1,...p)):p=[d],v+p.length}}function Vr(e){const t=[];if(!Array.isArray(e))throw new TypeError("Expected find and replace tuple or list of tuples");const r=!e[0]||Array.isArray(e[0])?e:[e];let n=-1;for(;++n<r.length;){const a=r[n];t.push([Ur(a[0]),Wr(a[1])])}return t}function Ur(e){return typeof e=="string"?new RegExp(Pr(e),"g"):e}function Wr(e){return typeof e=="function"?e:function(){return e}}const fe="phrasing",ge=["autolink","link","image","label"];function Hr(){return{transforms:[Xr],enter:{literalAutolink:qr,literalAutolinkEmail:xe,literalAutolinkHttp:xe,literalAutolinkWww:xe},exit:{literalAutolink:Jr,literalAutolinkEmail:Yr,literalAutolinkHttp:Gr,literalAutolinkWww:Kr}}}function Zr(){return{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:fe,notInConstruct:ge},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:fe,notInConstruct:ge},{character:":",before:"[ps]",after:"\\/",inConstruct:fe,notInConstruct:ge}]}}function qr(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function xe(e){this.config.enter.autolinkProtocol.call(this,e)}function Gr(e){this.config.exit.autolinkProtocol.call(this,e)}function Kr(e){this.config.exit.data.call(this,e);const t=this.stack[this.stack.length-1];F(t.type==="link"),t.url="http://"+this.sliceSerialize(e)}function Yr(e){this.config.exit.autolinkEmail.call(this,e)}function Jr(e){this.exit(e)}function Xr(e){Br(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,Qr],[/(?<=^|\s|\p{P}|\p{S})([-.\w+]+)@([-\w]+(?:\.[-\w]+)+)/gu,en]],{ignore:["link","linkReference"]})}function Qr(e,t,r,n,a){let s="";if(!dt(a)||(/^w/i.test(t)&&(r=t+r,t="",s="http://"),!tn(r)))return!1;const o=rn(r+n);if(!o[0])return!1;const c={type:"link",title:null,url:s+t+o[0],children:[{type:"text",value:t+o[0]}]};return o[1]?[c,{type:"text",value:o[1]}]:c}function en(e,t,r,n){return!dt(n,!0)||/[-\d_]$/.test(r)?!1:{type:"link",title:null,url:"mailto:"+t+"@"+r,children:[{type:"text",value:t+"@"+r}]}}function tn(e){const t=e.split(".");return!(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))}function rn(e){const t=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!t)return[e,void 0];e=e.slice(0,t.index);let r=t[0],n=r.indexOf(")");const a=Re(e,"(");let s=Re(e,")");for(;n!==-1&&a>s;)e+=r.slice(0,n+1),r=r.slice(n+1),n=r.indexOf(")"),s++;return[e,r]}function dt(e,t){const r=e.input.charCodeAt(e.index-1);return(e.index===0||U(r)||Se(r))&&(!t||r!==47)}ht.peek=hn;function nn(){this.buffer()}function an(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function sn(){this.buffer()}function on(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function ln(e){const t=this.resume(),r=this.stack[this.stack.length-1];F(r.type==="footnoteReference"),r.identifier=Y(this.sliceSerialize(e)).toLowerCase(),r.label=t}function cn(e){this.exit(e)}function un(e){const t=this.resume(),r=this.stack[this.stack.length-1];F(r.type==="footnoteDefinition"),r.identifier=Y(this.sliceSerialize(e)).toLowerCase(),r.label=t}function dn(e){this.exit(e)}function hn(){return"["}function ht(e,t,r,n){const a=r.createTracker(n);let s=a.move("[^");const o=r.enter("footnoteReference"),c=r.enter("reference");return s+=a.move(r.safe(r.associationId(e),{after:"]",before:s})),c(),o(),s+=a.move("]"),s}function mn(){return{enter:{gfmFootnoteCallString:nn,gfmFootnoteCall:an,gfmFootnoteDefinitionLabelString:sn,gfmFootnoteDefinition:on},exit:{gfmFootnoteCallString:ln,gfmFootnoteCall:cn,gfmFootnoteDefinitionLabelString:un,gfmFootnoteDefinition:dn}}}function fn(e){let t=!1;return e&&e.firstLineBlank&&(t=!0),{handlers:{footnoteDefinition:r,footnoteReference:ht},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]};function r(n,a,s,o){const c=s.createTracker(o);let u=c.move("[^");const d=s.enter("footnoteDefinition"),h=s.enter("label");return u+=c.move(s.safe(s.associationId(n),{before:u,after:"]"})),h(),u+=c.move("]:"),n.children&&n.children.length>0&&(c.shift(4),u+=c.move((t?`
`:" ")+s.indentLines(s.containerFlow(n,c.current()),t?mt:gn))),d(),u}}function gn(e,t,r){return t===0?e:mt(e,t,r)}function mt(e,t,r){return(r?"":"    ")+e}const xn=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];ft.peek=kn;function pn(){return{canContainEols:["delete"],enter:{strikethrough:bn},exit:{strikethrough:wn}}}function yn(){return{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:xn}],handlers:{delete:ft}}}function bn(e){this.enter({type:"delete",children:[]},e)}function wn(e){this.exit(e)}function ft(e,t,r,n){const a=r.createTracker(n),s=r.enter("strikethrough");let o=a.move("~~");return o+=r.containerPhrasing(e,{...a.current(),before:o,after:"~"}),o+=a.move("~~"),s(),o}function kn(){return"~"}function vn(e){return e.length}function jn(e,t){const r=t||{},n=(r.align||[]).concat(),a=r.stringLength||vn,s=[],o=[],c=[],u=[];let d=0,h=-1;for(;++h<e.length;){const y=[],v=[];let x=-1;for(e[h].length>d&&(d=e[h].length);++x<e[h].length;){const p=Nn(e[h][x]);if(r.alignDelimiters!==!1){const k=a(p);v[x]=k,(u[x]===void 0||k>u[x])&&(u[x]=k)}y.push(p)}o[h]=y,c[h]=v}let l=-1;if(typeof n=="object"&&"length"in n)for(;++l<d;)s[l]=Ve(n[l]);else{const y=Ve(n);for(;++l<d;)s[l]=y}l=-1;const b=[],f=[];for(;++l<d;){const y=s[l];let v="",x="";y===99?(v=":",x=":"):y===108?v=":":y===114&&(x=":");let p=r.alignDelimiters===!1?1:Math.max(1,u[l]-v.length-x.length);const k=v+"-".repeat(p)+x;r.alignDelimiters!==!1&&(p=v.length+p+x.length,p>u[l]&&(u[l]=p),f[l]=p),b[l]=k}o.splice(1,0,b),c.splice(1,0,f),h=-1;const g=[];for(;++h<o.length;){const y=o[h],v=c[h];l=-1;const x=[];for(;++l<d;){const p=y[l]||"";let k="",C="";if(r.alignDelimiters!==!1){const E=u[l]-(v[l]||0),j=s[l];j===114?k=" ".repeat(E):j===99?E%2?(k=" ".repeat(E/2+.5),C=" ".repeat(E/2-.5)):(k=" ".repeat(E/2),C=k):C=" ".repeat(E)}r.delimiterStart!==!1&&!l&&x.push("|"),r.padding!==!1&&!(r.alignDelimiters===!1&&p==="")&&(r.delimiterStart!==!1||l)&&x.push(" "),r.alignDelimiters!==!1&&x.push(k),x.push(p),r.alignDelimiters!==!1&&x.push(C),r.padding!==!1&&x.push(" "),(r.delimiterEnd!==!1||l!==d-1)&&x.push("|")}g.push(r.delimiterEnd===!1?x.join("").replace(/ +$/,""):x.join(""))}return g.join(`
`)}function Nn(e){return e==null?"":String(e)}function Ve(e){const t=typeof e=="string"?e.codePointAt(0):0;return t===67||t===99?99:t===76||t===108?108:t===82||t===114?114:0}function En(){return{enter:{table:Cn,tableData:Ue,tableHeader:Ue,tableRow:Ln},exit:{codeText:An,table:Sn,tableData:pe,tableHeader:pe,tableRow:pe}}}function Cn(e){const t=e._align;this.enter({type:"table",align:t.map(function(r){return r==="none"?null:r}),children:[]},e),this.data.inTable=!0}function Sn(e){this.exit(e),this.data.inTable=void 0}function Ln(e){this.enter({type:"tableRow",children:[]},e)}function pe(e){this.exit(e)}function Ue(e){this.enter({type:"tableCell",children:[]},e)}function An(e){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,Tn));const r=this.stack[this.stack.length-1];F(r.type==="inlineCode"),r.value=t,this.exit(e)}function Tn(e,t){return t==="|"?t:e}function Mn(e){const t=e||{},r=t.tableCellPadding,n=t.tablePipeAlign,a=t.stringLength,s=r?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:`
`,inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:b,table:o,tableCell:u,tableRow:c}};function o(f,g,y,v){return d(h(f,y,v),f.align)}function c(f,g,y,v){const x=l(f,y,v),p=d([x]);return p.slice(0,p.indexOf(`
`))}function u(f,g,y,v){const x=y.enter("tableCell"),p=y.enter("phrasing"),k=y.containerPhrasing(f,{...v,before:s,after:s});return p(),x(),k}function d(f,g){return jn(f,{align:g,alignDelimiters:n,padding:r,stringLength:a})}function h(f,g,y){const v=f.children;let x=-1;const p=[],k=g.enter("table");for(;++x<v.length;)p[x]=l(v[x],g,y);return k(),p}function l(f,g,y){const v=f.children;let x=-1;const p=[],k=g.enter("tableRow");for(;++x<v.length;)p[x]=u(v[x],f,g,y);return k(),p}function b(f,g,y){let v=at.inlineCode(f,g,y);return y.stack.includes("tableCell")&&(v=v.replace(/\|/g,"\\$&")),v}}function Dn(){return{exit:{taskListCheckValueChecked:We,taskListCheckValueUnchecked:We,paragraph:Fn}}}function In(){return{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:Rn}}}function We(e){const t=this.stack[this.stack.length-2];F(t.type==="listItem"),t.checked=e.type==="taskListCheckValueChecked"}function Fn(e){const t=this.stack[this.stack.length-2];if(t&&t.type==="listItem"&&typeof t.checked=="boolean"){const r=this.stack[this.stack.length-1];F(r.type==="paragraph");const n=r.children[0];if(n&&n.type==="text"){const a=t.children;let s=-1,o;for(;++s<a.length;){const c=a[s];if(c.type==="paragraph"){o=c;break}}o===r&&(n.value=n.value.slice(1),n.value.length===0?r.children.shift():r.position&&n.position&&typeof n.position.start.offset=="number"&&(n.position.start.column++,n.position.start.offset++,r.position.start=Object.assign({},n.position.start)))}}this.exit(e)}function Rn(e,t,r,n){const a=e.children[0],s=typeof e.checked=="boolean"&&a&&a.type==="paragraph",o="["+(e.checked?"x":" ")+"] ",c=r.createTracker(n);s&&c.move(o);let u=at.listItem(e,t,r,{...n,...c.current()});return s&&(u=u.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,d)),u;function d(h){return h+o}}function zn(){return[Hr(),mn(),pn(),En(),Dn()]}function _n(e){return{extensions:[Zr(),fn(e),yn(),Mn(e),In()]}}const On={tokenize:Wn,partial:!0},gt={tokenize:Hn,partial:!0},xt={tokenize:Zn,partial:!0},pt={tokenize:qn,partial:!0},$n={tokenize:Gn,partial:!0},yt={name:"wwwAutolink",tokenize:Vn,previous:wt},bt={name:"protocolAutolink",tokenize:Un,previous:kt},O={name:"emailAutolink",tokenize:Bn,previous:vt},z={};function Pn(){return{text:z}}let $=48;for(;$<123;)z[$]=O,$++,$===58?$=65:$===91&&($=97);z[43]=O;z[45]=O;z[46]=O;z[95]=O;z[72]=[O,bt];z[104]=[O,bt];z[87]=[O,yt];z[119]=[O,yt];function Bn(e,t,r){const n=this;let a,s;return o;function o(l){return!ke(l)||!vt.call(n,n.previous)||Ae(n.events)?r(l):(e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),c(l))}function c(l){return ke(l)?(e.consume(l),c):l===64?(e.consume(l),u):r(l)}function u(l){return l===46?e.check($n,h,d)(l):l===45||l===95||Le(l)?(s=!0,e.consume(l),u):h(l)}function d(l){return e.consume(l),a=!0,u}function h(l){return s&&a&&G(n.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),t(l)):r(l)}}function Vn(e,t,r){const n=this;return a;function a(o){return o!==87&&o!==119||!wt.call(n,n.previous)||Ae(n.events)?r(o):(e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(On,e.attempt(gt,e.attempt(xt,s),r),r)(o))}function s(o){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),t(o)}}function Un(e,t,r){const n=this;let a="",s=!1;return o;function o(l){return(l===72||l===104)&&kt.call(n,n.previous)&&!Ae(n.events)?(e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),a+=String.fromCodePoint(l),e.consume(l),c):r(l)}function c(l){if(G(l)&&a.length<5)return a+=String.fromCodePoint(l),e.consume(l),c;if(l===58){const b=a.toLowerCase();if(b==="http"||b==="https")return e.consume(l),u}return r(l)}function u(l){return l===47?(e.consume(l),s?d:(s=!0,u)):r(l)}function d(l){return l===null||Ut(l)||M(l)||U(l)||Se(l)?r(l):e.attempt(gt,e.attempt(xt,h),r)(l)}function h(l){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),t(l)}}function Wn(e,t,r){let n=0;return a;function a(o){return(o===87||o===119)&&n<3?(n++,e.consume(o),a):o===46&&n===3?(e.consume(o),s):r(o)}function s(o){return o===null?r(o):t(o)}}function Hn(e,t,r){let n,a,s;return o;function o(d){return d===46||d===95?e.check(pt,u,c)(d):d===null||M(d)||U(d)||d!==45&&Se(d)?u(d):(s=!0,e.consume(d),o)}function c(d){return d===95?n=!0:(a=n,n=void 0),e.consume(d),o}function u(d){return a||n||!s?r(d):t(d)}}function Zn(e,t){let r=0,n=0;return a;function a(o){return o===40?(r++,e.consume(o),a):o===41&&n<r?s(o):o===33||o===34||o===38||o===39||o===41||o===42||o===44||o===46||o===58||o===59||o===60||o===63||o===93||o===95||o===126?e.check(pt,t,s)(o):o===null||M(o)||U(o)?t(o):(e.consume(o),a)}function s(o){return o===41&&n++,e.consume(o),a}}function qn(e,t,r){return n;function n(c){return c===33||c===34||c===39||c===41||c===42||c===44||c===46||c===58||c===59||c===63||c===95||c===126?(e.consume(c),n):c===38?(e.consume(c),s):c===93?(e.consume(c),a):c===60||c===null||M(c)||U(c)?t(c):r(c)}function a(c){return c===null||c===40||c===91||M(c)||U(c)?t(c):n(c)}function s(c){return G(c)?o(c):r(c)}function o(c){return c===59?(e.consume(c),n):G(c)?(e.consume(c),o):r(c)}}function Gn(e,t,r){return n;function n(s){return e.consume(s),a}function a(s){return Le(s)?r(s):t(s)}}function wt(e){return e===null||e===40||e===42||e===95||e===91||e===93||e===126||M(e)}function kt(e){return!G(e)}function vt(e){return!(e===47||ke(e))}function ke(e){return e===43||e===45||e===46||e===95||Le(e)}function Ae(e){let t=e.length,r=!1;for(;t--;){const n=e[t][1];if((n.type==="labelLink"||n.type==="labelImage")&&!n._balanced){r=!0;break}if(n._gfmAutolinkLiteralWalkedInto){r=!1;break}}return e.length>0&&!r&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),r}const Kn={tokenize:na,partial:!0};function Yn(){return{document:{91:{name:"gfmFootnoteDefinition",tokenize:ea,continuation:{tokenize:ta},exit:ra}},text:{91:{name:"gfmFootnoteCall",tokenize:Qn},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:Jn,resolveTo:Xn}}}}function Jn(e,t,r){const n=this;let a=n.events.length;const s=n.parser.gfmFootnotes||(n.parser.gfmFootnotes=[]);let o;for(;a--;){const u=n.events[a][1];if(u.type==="labelImage"){o=u;break}if(u.type==="gfmFootnoteCall"||u.type==="labelLink"||u.type==="label"||u.type==="image"||u.type==="link")break}return c;function c(u){if(!o||!o._balanced)return r(u);const d=Y(n.sliceSerialize({start:o.end,end:n.now()}));return d.codePointAt(0)!==94||!s.includes(d.slice(1))?r(u):(e.enter("gfmFootnoteCallLabelMarker"),e.consume(u),e.exit("gfmFootnoteCallLabelMarker"),t(u))}}function Xn(e,t){let r=e.length;for(;r--;)if(e[r][1].type==="labelImage"&&e[r][0]==="enter"){e[r][1];break}e[r+1][1].type="data",e[r+3][1].type="gfmFootnoteCallLabelMarker";const n={type:"gfmFootnoteCall",start:Object.assign({},e[r+3][1].start),end:Object.assign({},e[e.length-1][1].end)},a={type:"gfmFootnoteCallMarker",start:Object.assign({},e[r+3][1].end),end:Object.assign({},e[r+3][1].end)};a.end.column++,a.end.offset++,a.end._bufferIndex++;const s={type:"gfmFootnoteCallString",start:Object.assign({},a.end),end:Object.assign({},e[e.length-1][1].start)},o={type:"chunkString",contentType:"string",start:Object.assign({},s.start),end:Object.assign({},s.end)},c=[e[r+1],e[r+2],["enter",n,t],e[r+3],e[r+4],["enter",a,t],["exit",a,t],["enter",s,t],["enter",o,t],["exit",o,t],["exit",s,t],e[e.length-2],e[e.length-1],["exit",n,t]];return e.splice(r,e.length-r+1,...c),e}function Qn(e,t,r){const n=this,a=n.parser.gfmFootnotes||(n.parser.gfmFootnotes=[]);let s=0,o;return c;function c(l){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(l),e.exit("gfmFootnoteCallLabelMarker"),u}function u(l){return l!==94?r(l):(e.enter("gfmFootnoteCallMarker"),e.consume(l),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",d)}function d(l){if(s>999||l===93&&!o||l===null||l===91||M(l))return r(l);if(l===93){e.exit("chunkString");const b=e.exit("gfmFootnoteCallString");return a.includes(Y(n.sliceSerialize(b)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(l),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),t):r(l)}return M(l)||(o=!0),s++,e.consume(l),l===92?h:d}function h(l){return l===91||l===92||l===93?(e.consume(l),s++,d):d(l)}}function ea(e,t,r){const n=this,a=n.parser.gfmFootnotes||(n.parser.gfmFootnotes=[]);let s,o=0,c;return u;function u(g){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(g),e.exit("gfmFootnoteDefinitionLabelMarker"),d}function d(g){return g===94?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(g),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",h):r(g)}function h(g){if(o>999||g===93&&!c||g===null||g===91||M(g))return r(g);if(g===93){e.exit("chunkString");const y=e.exit("gfmFootnoteDefinitionLabelString");return s=Y(n.sliceSerialize(y)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(g),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),b}return M(g)||(c=!0),o++,e.consume(g),g===92?l:h}function l(g){return g===91||g===92||g===93?(e.consume(g),o++,h):h(g)}function b(g){return g===58?(e.enter("definitionMarker"),e.consume(g),e.exit("definitionMarker"),a.includes(s)||a.push(s),I(e,f,"gfmFootnoteDefinitionWhitespace")):r(g)}function f(g){return t(g)}}function ta(e,t,r){return e.check(Wt,t,e.attempt(Kn,t,r))}function ra(e){e.exit("gfmFootnoteDefinition")}function na(e,t,r){const n=this;return I(e,a,"gfmFootnoteDefinitionIndent",4+1);function a(s){const o=n.events[n.events.length-1];return o&&o[1].type==="gfmFootnoteDefinitionIndent"&&o[2].sliceSerialize(o[1],!0).length===4?t(s):r(s)}}function aa(e){let r=(e||{}).singleTilde;const n={name:"strikethrough",tokenize:s,resolveAll:a};return r==null&&(r=!0),{text:{126:n},insideSpan:{null:[n]},attentionMarkers:{null:[126]}};function a(o,c){let u=-1;for(;++u<o.length;)if(o[u][0]==="enter"&&o[u][1].type==="strikethroughSequenceTemporary"&&o[u][1]._close){let d=u;for(;d--;)if(o[d][0]==="exit"&&o[d][1].type==="strikethroughSequenceTemporary"&&o[d][1]._open&&o[u][1].end.offset-o[u][1].start.offset===o[d][1].end.offset-o[d][1].start.offset){o[u][1].type="strikethroughSequence",o[d][1].type="strikethroughSequence";const h={type:"strikethrough",start:Object.assign({},o[d][1].start),end:Object.assign({},o[u][1].end)},l={type:"strikethroughText",start:Object.assign({},o[d][1].end),end:Object.assign({},o[u][1].start)},b=[["enter",h,c],["enter",o[d][1],c],["exit",o[d][1],c],["enter",l,c]],f=c.parser.constructs.insideSpan.null;f&&ce(b,b.length,0,Ht(f,o.slice(d+1,u),c)),ce(b,b.length,0,[["exit",l,c],["enter",o[u][1],c],["exit",o[u][1],c],["exit",h,c]]),ce(o,d-1,u-d+3,b),u=d+b.length-2;break}}for(u=-1;++u<o.length;)o[u][1].type==="strikethroughSequenceTemporary"&&(o[u][1].type="data");return o}function s(o,c,u){const d=this.previous,h=this.events;let l=0;return b;function b(g){return d===126&&h[h.length-1][1].type!=="characterEscape"?u(g):(o.enter("strikethroughSequenceTemporary"),f(g))}function f(g){const y=ze(d);if(g===126)return l>1?u(g):(o.consume(g),l++,f);if(l<2&&!r)return u(g);const v=o.exit("strikethroughSequenceTemporary"),x=ze(g);return v._open=!x||x===2&&!!y,v._close=!y||y===2&&!!x,c(g)}}}class ia{constructor(){this.map=[]}add(t,r,n){sa(this,t,r,n)}consume(t){if(this.map.sort(function(s,o){return s[0]-o[0]}),this.map.length===0)return;let r=this.map.length;const n=[];for(;r>0;)r-=1,n.push(t.slice(this.map[r][0]+this.map[r][1]),this.map[r][2]),t.length=this.map[r][0];n.push(t.slice()),t.length=0;let a=n.pop();for(;a;){for(const s of a)t.push(s);a=n.pop()}this.map.length=0}}function sa(e,t,r,n){let a=0;if(!(r===0&&n.length===0)){for(;a<e.map.length;){if(e.map[a][0]===t){e.map[a][1]+=r,e.map[a][2].push(...n);return}a+=1}e.map.push([t,r,n])}}function oa(e,t){let r=!1;const n=[];for(;t<e.length;){const a=e[t];if(r){if(a[0]==="enter")a[1].type==="tableContent"&&n.push(e[t+1][1].type==="tableDelimiterMarker"?"left":"none");else if(a[1].type==="tableContent"){if(e[t-1][1].type==="tableDelimiterMarker"){const s=n.length-1;n[s]=n[s]==="left"?"center":"right"}}else if(a[1].type==="tableDelimiterRow")break}else a[0]==="enter"&&a[1].type==="tableDelimiterRow"&&(r=!0);t+=1}return n}function la(){return{flow:{null:{name:"table",tokenize:ca,resolveAll:ua}}}}function ca(e,t,r){const n=this;let a=0,s=0,o;return c;function c(w){let H=n.events.length-1;for(;H>-1;){const Fe=n.events[H][1].type;if(Fe==="lineEnding"||Fe==="linePrefix")H--;else break}const De=H>-1?n.events[H][1].type:null,Ie=De==="tableHead"||De==="tableRow"?j:u;return Ie===j&&n.parser.lazy[n.now().line]?r(w):Ie(w)}function u(w){return e.enter("tableHead"),e.enter("tableRow"),d(w)}function d(w){return w===124||(o=!0,s+=1),h(w)}function h(w){return w===null?r(w):T(w)?s>1?(s=0,n.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(w),e.exit("lineEnding"),f):r(w):R(w)?I(e,h,"whitespace")(w):(s+=1,o&&(o=!1,a+=1),w===124?(e.enter("tableCellDivider"),e.consume(w),e.exit("tableCellDivider"),o=!0,h):(e.enter("data"),l(w)))}function l(w){return w===null||w===124||M(w)?(e.exit("data"),h(w)):(e.consume(w),w===92?b:l)}function b(w){return w===92||w===124?(e.consume(w),l):l(w)}function f(w){return n.interrupt=!1,n.parser.lazy[n.now().line]?r(w):(e.enter("tableDelimiterRow"),o=!1,R(w)?I(e,g,"linePrefix",n.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(w):g(w))}function g(w){return w===45||w===58?v(w):w===124?(o=!0,e.enter("tableCellDivider"),e.consume(w),e.exit("tableCellDivider"),y):E(w)}function y(w){return R(w)?I(e,v,"whitespace")(w):v(w)}function v(w){return w===58?(s+=1,o=!0,e.enter("tableDelimiterMarker"),e.consume(w),e.exit("tableDelimiterMarker"),x):w===45?(s+=1,x(w)):w===null||T(w)?C(w):E(w)}function x(w){return w===45?(e.enter("tableDelimiterFiller"),p(w)):E(w)}function p(w){return w===45?(e.consume(w),p):w===58?(o=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(w),e.exit("tableDelimiterMarker"),k):(e.exit("tableDelimiterFiller"),k(w))}function k(w){return R(w)?I(e,C,"whitespace")(w):C(w)}function C(w){return w===124?g(w):w===null||T(w)?!o||a!==s?E(w):(e.exit("tableDelimiterRow"),e.exit("tableHead"),t(w)):E(w)}function E(w){return r(w)}function j(w){return e.enter("tableRow"),N(w)}function N(w){return w===124?(e.enter("tableCellDivider"),e.consume(w),e.exit("tableCellDivider"),N):w===null||T(w)?(e.exit("tableRow"),t(w)):R(w)?I(e,N,"whitespace")(w):(e.enter("data"),S(w))}function S(w){return w===null||w===124||M(w)?(e.exit("data"),N(w)):(e.consume(w),w===92?A:S)}function A(w){return w===92||w===124?(e.consume(w),S):S(w)}}function ua(e,t){let r=-1,n=!0,a=0,s=[0,0,0,0],o=[0,0,0,0],c=!1,u=0,d,h,l;const b=new ia;for(;++r<e.length;){const f=e[r],g=f[1];f[0]==="enter"?g.type==="tableHead"?(c=!1,u!==0&&(He(b,t,u,d,h),h=void 0,u=0),d={type:"table",start:Object.assign({},g.start),end:Object.assign({},g.end)},b.add(r,0,[["enter",d,t]])):g.type==="tableRow"||g.type==="tableDelimiterRow"?(n=!0,l=void 0,s=[0,0,0,0],o=[0,r+1,0,0],c&&(c=!1,h={type:"tableBody",start:Object.assign({},g.start),end:Object.assign({},g.end)},b.add(r,0,[["enter",h,t]])),a=g.type==="tableDelimiterRow"?2:h?3:1):a&&(g.type==="data"||g.type==="tableDelimiterMarker"||g.type==="tableDelimiterFiller")?(n=!1,o[2]===0&&(s[1]!==0&&(o[0]=o[1],l=te(b,t,s,a,void 0,l),s=[0,0,0,0]),o[2]=r)):g.type==="tableCellDivider"&&(n?n=!1:(s[1]!==0&&(o[0]=o[1],l=te(b,t,s,a,void 0,l)),s=o,o=[s[1],r,0,0])):g.type==="tableHead"?(c=!0,u=r):g.type==="tableRow"||g.type==="tableDelimiterRow"?(u=r,s[1]!==0?(o[0]=o[1],l=te(b,t,s,a,r,l)):o[1]!==0&&(l=te(b,t,o,a,r,l)),a=0):a&&(g.type==="data"||g.type==="tableDelimiterMarker"||g.type==="tableDelimiterFiller")&&(o[3]=r)}for(u!==0&&He(b,t,u,d,h),b.consume(t.events),r=-1;++r<t.events.length;){const f=t.events[r];f[0]==="enter"&&f[1].type==="table"&&(f[1]._align=oa(t.events,r))}return e}function te(e,t,r,n,a,s){const o=n===1?"tableHeader":n===2?"tableDelimiter":"tableData",c="tableContent";r[0]!==0&&(s.end=Object.assign({},V(t.events,r[0])),e.add(r[0],0,[["exit",s,t]]));const u=V(t.events,r[1]);if(s={type:o,start:Object.assign({},u),end:Object.assign({},u)},e.add(r[1],0,[["enter",s,t]]),r[2]!==0){const d=V(t.events,r[2]),h=V(t.events,r[3]),l={type:c,start:Object.assign({},d),end:Object.assign({},h)};if(e.add(r[2],0,[["enter",l,t]]),n!==2){const b=t.events[r[2]],f=t.events[r[3]];if(b[1].end=Object.assign({},f[1].end),b[1].type="chunkText",b[1].contentType="text",r[3]>r[2]+1){const g=r[2]+1,y=r[3]-r[2]-1;e.add(g,y,[])}}e.add(r[3]+1,0,[["exit",l,t]])}return a!==void 0&&(s.end=Object.assign({},V(t.events,a)),e.add(a,0,[["exit",s,t]]),s=void 0),s}function He(e,t,r,n,a){const s=[],o=V(t.events,r);a&&(a.end=Object.assign({},o),s.push(["exit",a,t])),n.end=Object.assign({},o),s.push(["exit",n,t]),e.add(r+1,0,s)}function V(e,t){const r=e[t],n=r[0]==="enter"?"start":"end";return r[1][n]}const da={name:"tasklistCheck",tokenize:ma};function ha(){return{text:{91:da}}}function ma(e,t,r){const n=this;return a;function a(u){return n.previous!==null||!n._gfmTasklistFirstContentOfListItem?r(u):(e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(u),e.exit("taskListCheckMarker"),s)}function s(u){return M(u)?(e.enter("taskListCheckValueUnchecked"),e.consume(u),e.exit("taskListCheckValueUnchecked"),o):u===88||u===120?(e.enter("taskListCheckValueChecked"),e.consume(u),e.exit("taskListCheckValueChecked"),o):r(u)}function o(u){return u===93?(e.enter("taskListCheckMarker"),e.consume(u),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),c):r(u)}function c(u){return T(u)?t(u):R(u)?e.check({tokenize:fa},t,r)(u):r(u)}}function fa(e,t,r){return I(e,n,"whitespace");function n(a){return a===null?r(a):t(a)}}function ga(e){return Zt([Pn(),Yn(),aa(e),la(),ha()])}const xa={};function pa(e){const t=this,r=e||xa,n=t.data(),a=n.micromarkExtensions||(n.micromarkExtensions=[]),s=n.fromMarkdownExtensions||(n.fromMarkdownExtensions=[]),o=n.toMarkdownExtensions||(n.toMarkdownExtensions=[]);a.push(ga(r)),s.push(zn()),o.push(_n(r))}var jt={exports:{}};(function(e){(function(){var t;t=e.exports=a,t.format=a,t.vsprintf=n,typeof console<"u"&&typeof console.log=="function"&&(t.printf=r);function r(){console.log(a.apply(null,arguments))}function n(s,o){return a.apply(null,[s].concat(o))}function a(s){for(var o=1,c=[].slice.call(arguments),u=0,d=s.length,h="",l,b=!1,f,g,y=!1,v,x=function(){return c[o++]},p=function(){for(var k="";/\d/.test(s[u]);)k+=s[u++],l=s[u];return k.length>0?parseInt(k):null};u<d;++u)if(l=s[u],b)switch(b=!1,l=="."?(y=!1,l=s[++u]):l=="0"&&s[u+1]=="."?(y=!0,u+=2,l=s[u]):y=!0,v=p(),l){case"b":h+=parseInt(x(),10).toString(2);break;case"c":f=x(),typeof f=="string"||f instanceof String?h+=f:h+=String.fromCharCode(parseInt(f,10));break;case"d":h+=parseInt(x(),10);break;case"f":g=String(parseFloat(x()).toFixed(v||6)),h+=y?g:g.replace(/^0/,"");break;case"j":h+=JSON.stringify(x());break;case"o":h+="0"+parseInt(x(),10).toString(8);break;case"s":h+=x();break;case"x":h+="0x"+parseInt(x(),10).toString(16);break;case"X":h+="0x"+parseInt(x(),10).toString(16).toUpperCase();break;default:h+=l;break}else l==="%"?b=!0:h+=l;return h}})()})(jt);var ya=jt.exports;const ba=Ce(ya),re=Object.assign(P(Error),{eval:P(EvalError),range:P(RangeError),reference:P(ReferenceError),syntax:P(SyntaxError),type:P(TypeError),uri:P(URIError)});function P(e){return t.displayName=e.displayName||e.name,t;function t(r,...n){const a=r&&ba(r,...n);return new e(a)}}const ne={}.hasOwnProperty,Ze={yaml:"-",toml:"+"};function Te(e){const t=[];let r=-1;const n=Array.isArray(e)?e:e?[e]:["yaml"];for(;++r<n.length;)t[r]=wa(n[r]);return t}function wa(e){let t=e;if(typeof t=="string"){if(!ne.call(Ze,t))throw re("Missing matter definition for `%s`",t);t={type:t,marker:Ze[t]}}else if(typeof t!="object")throw re("Expected matter to be an object, not `%j`",t);if(!ne.call(t,"type"))throw re("Missing `type` in matter `%j`",t);if(!ne.call(t,"fence")&&!ne.call(t,"marker"))throw re("Missing `marker` or `fence` in matter `%j`",t);return t}function ka(e){const t=Te(e),r={};let n=-1;for(;++n<t.length;){const a=t[n],s=ve(a,"open").charCodeAt(0),o=va(a),c=r[s];Array.isArray(c)?c.push(o):r[s]=[o]}return{flow:r}}function va(e){const t=e.anywhere,r=e.type,n=r+"Fence",a=n+"Sequence",s=r+"Value",o={tokenize:h,partial:!0};let c,u=0;return{tokenize:d,concrete:!0};function d(l,b,f){const g=this;return y;function y(N){const S=g.now();return S.column===1&&(S.line===1||t)&&(c=ve(e,"open"),u=0,N===c.charCodeAt(u))?(l.enter(r),l.enter(n),l.enter(a),v(N)):f(N)}function v(N){return u===c.length?(l.exit(a),R(N)?(l.enter("whitespace"),x(N)):p(N)):N===c.charCodeAt(u++)?(l.consume(N),v):f(N)}function x(N){return R(N)?(l.consume(N),x):(l.exit("whitespace"),p(N))}function p(N){return T(N)?(l.exit(n),l.enter("lineEnding"),l.consume(N),l.exit("lineEnding"),c=ve(e,"close"),u=0,l.attempt(o,j,k)):f(N)}function k(N){return N===null||T(N)?E(N):(l.enter(s),C(N))}function C(N){return N===null||T(N)?(l.exit(s),E(N)):(l.consume(N),C)}function E(N){return N===null?f(N):(l.enter("lineEnding"),l.consume(N),l.exit("lineEnding"),l.attempt(o,j,k))}function j(N){return l.exit(r),b(N)}}function h(l,b,f){let g=0;return y;function y(k){return k===c.charCodeAt(g)?(l.enter(n),l.enter(a),v(k)):f(k)}function v(k){return g===c.length?(l.exit(a),R(k)?(l.enter("whitespace"),x(k)):p(k)):k===c.charCodeAt(g++)?(l.consume(k),v):f(k)}function x(k){return R(k)?(l.consume(k),x):(l.exit("whitespace"),p(k))}function p(k){return k===null||T(k)?(l.exit(n),b(k)):f(k)}}}function ve(e,t){return e.marker?qe(e.marker,t).repeat(3):qe(e.fence,t)}function qe(e,t){return typeof e=="string"?e:e[t]}function ja(e){if(typeof e!="string")throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}function Na(e){const t=Te(e),r={},n={};let a=-1;for(;++a<t.length;){const s=t[a];r[s.type]=Ea(s),n[s.type]=Ca,n[s.type+"Value"]=Sa}return{enter:r,exit:n}}function Ea(e){return t;function t(r){this.enter({type:e.type,value:""},r),this.buffer()}}function Ca(e){const t=this.resume(),r=this.stack[this.stack.length-1];this.exit(e),r.value=t.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,"")}function Sa(e){this.config.enter.data.call(this,e),this.config.exit.data.call(this,e)}function La(e){const t=[],r={},n=Te(e);let a=-1;for(;++a<n.length;){const s=n[a];r[s.type]=Aa(s);const o=je(s,"open");t.push({atBreak:!0,character:o.charAt(0),after:ja(o.charAt(1))})}return{unsafe:t,handlers:r}}function Aa(e){const t=je(e,"open"),r=je(e,"close");return n;function n(a){return t+(a.value?`
`+a.value:"")+`
`+r}}function je(e,t){return e.marker?Ge(e.marker,t).repeat(3):Ge(e.fence,t)}function Ge(e,t){return typeof e=="string"?e:e[t]}const Ta="yaml";function Ma(e){const t=this,r=e||Ta,n=t.data(),a=n.micromarkExtensions||(n.micromarkExtensions=[]),s=n.fromMarkdownExtensions||(n.fromMarkdownExtensions=[]),o=n.toMarkdownExtensions||(n.toMarkdownExtensions=[]);a.push(ka(r)),s.push(Na(r)),o.push(La(r))}function Da(){return{enter:{mathFlow:e,mathFlowFenceMeta:t,mathText:s},exit:{mathFlow:a,mathFlowFence:n,mathFlowFenceMeta:r,mathFlowValue:c,mathText:o,mathTextData:c}};function e(u){const d={type:"element",tagName:"code",properties:{className:["language-math","math-display"]},children:[]};this.enter({type:"math",meta:null,value:"",data:{hName:"pre",hChildren:[d]}},u)}function t(){this.buffer()}function r(){const u=this.resume(),d=this.stack[this.stack.length-1];F(d.type==="math"),d.meta=u}function n(){this.data.mathFlowInside||(this.buffer(),this.data.mathFlowInside=!0)}function a(u){const d=this.resume().replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),h=this.stack[this.stack.length-1];F(h.type==="math"),this.exit(u),h.value=d;const l=h.data.hChildren[0];F(l.type==="element"),F(l.tagName==="code"),l.children.push({type:"text",value:d}),this.data.mathFlowInside=void 0}function s(u){this.enter({type:"inlineMath",value:"",data:{hName:"code",hProperties:{className:["language-math","math-inline"]},hChildren:[]}},u),this.buffer()}function o(u){const d=this.resume(),h=this.stack[this.stack.length-1];F(h.type==="inlineMath"),this.exit(u),h.value=d,h.data.hChildren.push({type:"text",value:d})}function c(u){this.config.enter.data.call(this,u),this.config.exit.data.call(this,u)}}function Ia(e){let t=(e||{}).singleDollarTextMath;return t==null&&(t=!0),n.peek=a,{unsafe:[{character:"\r",inConstruct:"mathFlowMeta"},{character:`
`,inConstruct:"mathFlowMeta"},{character:"$",after:t?void 0:"\\$",inConstruct:"phrasing"},{character:"$",inConstruct:"mathFlowMeta"},{atBreak:!0,character:"$",after:"\\$"}],handlers:{math:r,inlineMath:n}};function r(s,o,c,u){const d=s.value||"",h=c.createTracker(u),l="$".repeat(Math.max(qt(d,"$")+1,2)),b=c.enter("mathFlow");let f=h.move(l);if(s.meta){const g=c.enter("mathFlowMeta");f+=h.move(c.safe(s.meta,{after:`
`,before:f,encode:["$"],...h.current()})),g()}return f+=h.move(`
`),d&&(f+=h.move(d+`
`)),f+=h.move(l),b(),f}function n(s,o,c){let u=s.value||"",d=1;for(t||d++;new RegExp("(^|[^$])"+"\\$".repeat(d)+"([^$]|$)").test(u);)d++;const h="$".repeat(d);/[^ \r\n]/.test(u)&&(/^[ \r\n]/.test(u)&&/[ \r\n]$/.test(u)||/^\$|\$$/.test(u))&&(u=" "+u+" ");let l=-1;for(;++l<c.unsafe.length;){const b=c.unsafe[l];if(!b.atBreak)continue;const f=c.compilePattern(b);let g;for(;g=f.exec(u);){let y=g.index;u.codePointAt(y)===10&&u.codePointAt(y-1)===13&&y--,u=u.slice(0,y)+" "+u.slice(g.index+1)}}return h+u+h}function a(){return"$"}}const Fa={tokenize:Ra,concrete:!0,name:"mathFlow"},Ke={tokenize:za,partial:!0};function Ra(e,t,r){const n=this,a=n.events[n.events.length-1],s=a&&a[1].type==="linePrefix"?a[2].sliceSerialize(a[1],!0).length:0;let o=0;return c;function c(p){return e.enter("mathFlow"),e.enter("mathFlowFence"),e.enter("mathFlowFenceSequence"),u(p)}function u(p){return p===36?(e.consume(p),o++,u):o<2?r(p):(e.exit("mathFlowFenceSequence"),I(e,d,"whitespace")(p))}function d(p){return p===null||T(p)?l(p):(e.enter("mathFlowFenceMeta"),e.enter("chunkString",{contentType:"string"}),h(p))}function h(p){return p===null||T(p)?(e.exit("chunkString"),e.exit("mathFlowFenceMeta"),l(p)):p===36?r(p):(e.consume(p),h)}function l(p){return e.exit("mathFlowFence"),n.interrupt?t(p):e.attempt(Ke,b,v)(p)}function b(p){return e.attempt({tokenize:x,partial:!0},v,f)(p)}function f(p){return(s?I(e,g,"linePrefix",s+1):g)(p)}function g(p){return p===null?v(p):T(p)?e.attempt(Ke,b,v)(p):(e.enter("mathFlowValue"),y(p))}function y(p){return p===null||T(p)?(e.exit("mathFlowValue"),g(p)):(e.consume(p),y)}function v(p){return e.exit("mathFlow"),t(p)}function x(p,k,C){let E=0;return I(p,j,"linePrefix",n.parser.constructs.disable.null.includes("codeIndented")?void 0:4);function j(A){return p.enter("mathFlowFence"),p.enter("mathFlowFenceSequence"),N(A)}function N(A){return A===36?(E++,p.consume(A),N):E<o?C(A):(p.exit("mathFlowFenceSequence"),I(p,S,"whitespace")(A))}function S(A){return A===null||T(A)?(p.exit("mathFlowFence"),k(A)):C(A)}}}function za(e,t,r){const n=this;return a;function a(o){return o===null?t(o):(e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),s)}function s(o){return n.parser.lazy[n.now().line]?r(o):t(o)}}function _a(e){let r=(e||{}).singleDollarTextMath;return r==null&&(r=!0),{tokenize:n,resolve:Oa,previous:$a,name:"mathText"};function n(a,s,o){let c=0,u,d;return h;function h(y){return a.enter("mathText"),a.enter("mathTextSequence"),l(y)}function l(y){return y===36?(a.consume(y),c++,l):c<2&&!r?o(y):(a.exit("mathTextSequence"),b(y))}function b(y){return y===null?o(y):y===36?(d=a.enter("mathTextSequence"),u=0,g(y)):y===32?(a.enter("space"),a.consume(y),a.exit("space"),b):T(y)?(a.enter("lineEnding"),a.consume(y),a.exit("lineEnding"),b):(a.enter("mathTextData"),f(y))}function f(y){return y===null||y===32||y===36||T(y)?(a.exit("mathTextData"),b(y)):(a.consume(y),f)}function g(y){return y===36?(a.consume(y),u++,g):u===c?(a.exit("mathTextSequence"),a.exit("mathText"),s(y)):(d.type="mathTextData",f(y))}}}function Oa(e){let t=e.length-4,r=3,n,a;if((e[r][1].type==="lineEnding"||e[r][1].type==="space")&&(e[t][1].type==="lineEnding"||e[t][1].type==="space")){for(n=r;++n<t;)if(e[n][1].type==="mathTextData"){e[t][1].type="mathTextPadding",e[r][1].type="mathTextPadding",r+=2,t-=2;break}}for(n=r-1,t++;++n<=t;)a===void 0?n!==t&&e[n][1].type!=="lineEnding"&&(a=n):(n===t||e[n][1].type==="lineEnding")&&(e[a][1].type="mathTextData",n!==a+2&&(e[a][1].end=e[n-1][1].end,e.splice(a+2,n-a-2),t-=n-a-2,n=a+2),a=void 0);return e}function $a(e){return e!==36||this.events[this.events.length-1][1].type==="characterEscape"}function Pa(e){return{flow:{36:Fa},text:{36:_a(e)}}}const Ba={};function Va(e){const t=this,r=e||Ba,n=t.data(),a=n.micromarkExtensions||(n.micromarkExtensions=[]),s=n.fromMarkdownExtensions||(n.fromMarkdownExtensions=[]),o=n.toMarkdownExtensions||(n.toMarkdownExtensions=[]);a.push(Pa(r)),s.push(Da()),o.push(Ia(r))}const Ye=10*1024*1024,Ua={maxDepth:6,includeContent:!0,preserveWhitespace:!1,extractMetadata:!0,customPlugins:[]};class Wa{constructor(){D(this,"processor");this.processor=Gt().use(Kt).use(pa).use(Ma).use(Va)}async parseDocument(t,r={}){const n={...Ua,...r},a=this.validateInput(t);if(!a.isValid)throw new Error(`Validation failed: ${a.errors.map(s=>s.message).join(", ")}`);try{const s=this.preprocessMarkdown(t),o=this.processor.parse(s),c=await this.processor.run(o),u=this.buildTree(c,n),d=n.extractMetadata?this.extractMetadata(u):this.createEmptyMetadata(),h=this.calculateStatistics(u);return{tree:u,metadata:d,statistics:h,errors:[],warnings:a.warnings}}catch(s){throw new Error(`Parse failed: ${s instanceof Error?s.message:"Unknown error"}`)}}validateInput(t){const r=[],n=[];t.length>Ye&&r.push({type:"SIZE_EXCEEDED",message:`Input size exceeds maximum limit of ${Ye} characters`}),t.trim()||n.push({type:"EMPTY_INPUT",message:"Input is empty or contains only whitespace"});const a=this.validateHeadingStructure(t);return n.push(...a),{isValid:r.length===0,errors:r,warnings:n}}validateHeadingStructure(t){const r=[],n=t.split(`
`),a=[];n.forEach((s,o)=>{const c=s.match(/^(#{1,6})\s+(.+)/);c&&c[1]&&a.push({level:c[1].length,line:o+1})});for(let s=1;s<a.length;s++){const o=a[s],c=a[s-1];if(o&&c){const u=o.level,d=c.level;u-d>1&&r.push({type:"SKIPPED_HEADING_LEVEL",message:`Heading level skipped from H${d} to H${u}`,line:o.line})}}return r}preprocessMarkdown(t){let r=t.replace(/^\uFEFF/,"");return r=r.replace(/\r\n/g,`
`).replace(/\r/g,`
`),r.endsWith(`
`)||(r+=`
`),r}buildTree(t,r){const n={id:"root",level:0,title:"Document Root",content:"",rawContent:"",metadata:this.createEmptyNodeMetadata(),children:[]},a=[n],s=[];return Q(t,"heading",o=>{o.depth<=r.maxDepth&&s.push(o)}),s.forEach((o,c)=>{const u=this.createNodeFromHeading(o,t,c,s,r);this.insertNode(a,u)}),n}createNodeFromHeading(t,r,n,a,s){const o=this.extractHeadingText(t),c=s.includeContent?this.extractAssociatedContent(r,t,a,n):"";return{id:this.generateNodeId(o,t.depth),level:t.depth,title:o,content:c,rawContent:c,metadata:this.calculateNodeMetadata(c),children:[]}}extractHeadingText(t){let r="";return Q(t,"text",n=>{r+=n.value}),r.trim()||"Untitled"}extractAssociatedContent(t,r,n,a){const s=[];let o=!1;const c=n[a+1],u=r.depth;return Q(t,d=>{if(d===r){o=!0;return}if(o){if(d.type==="heading"&&d.depth<=u){o=!1;return}if(c&&d===c){o=!1;return}(d.type==="paragraph"||d.type==="list"||d.type==="code"||d.type==="blockquote")&&s.push(this.nodeToMarkdown(d))}}),s.join(`

`).trim()}nodeToMarkdown(t){switch(t.type){case"paragraph":return this.extractTextFromNode(t);case"list":return this.listToMarkdown(t);case"code":return`\`\`\`${t.lang||""}
${t.value}
\`\`\``;case"blockquote":return`> ${this.extractTextFromNode(t)}`;default:return this.extractTextFromNode(t)}}extractTextFromNode(t){let r="";return Q(t,"text",n=>{r+=n.value}),r}listToMarkdown(t){const r=[];return t.children&&t.children.forEach((n,a)=>{const s=this.extractTextFromNode(n),o=t.ordered?`${a+1}. `:"- ";r.push(o+s)}),r.join(`
`)}insertNode(t,r){for(;t.length>1;){const a=t[t.length-1];if(a&&a.level>=r.level)t.pop();else break}const n=t[t.length-1];n&&(r.parent=n,n.children.push(r)),t.push(r)}generateNodeId(t,r){const n=t.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-").substring(0,50);return`${r}-${n}-${Math.random().toString(36).substr(2,9)}`}calculateNodeMetadata(t){const r=this.countWords(t),n=t.length;return{wordCount:r,characterCount:n,hasImages:/!\[.*?\]\(.*?\)/.test(t),hasLinks:/\[.*?\]\(.*?\)/.test(t),hasCode:/```[\s\S]*?```|`[^`]+`/.test(t),hasTables:/\|.*\|/.test(t),hasLists:/^[\s]*[-*+]\s|^[\s]*\d+\.\s/m.test(t),complexity:this.calculateComplexity(t),estimatedReadTime:Math.ceil(r/200)}}countWords(t){return t.replace(/[^\w\s]/g," ").split(/\s+/).filter(r=>r.length>0).length}calculateComplexity(t){const r=this.countWords(t),n=/```[\s\S]*?```|\|.*\|/.test(t);return r<50&&!n?"low":r<200&&!n?"medium":"high"}extractMetadata(t){const r=this.calculateStatistics(t);return{title:t.children[0]?.title||"Untitled Document",totalNodes:r.nodeCount,maxDepth:r.maxDepth,totalWordCount:this.getTotalWordCount(t),estimatedReadTime:Math.ceil(this.getTotalWordCount(t)/200),hasImages:this.hasContentType(t,"hasImages"),hasCode:this.hasContentType(t,"hasCode"),hasTables:this.hasContentType(t,"hasTables"),complexity:this.getOverallComplexity(t)}}calculateStatistics(t){const r=this.countNodes(t),n=this.calculateMaxDepth(t),a=this.calculateBranchingFactor(t),s=this.analyzeContentDistribution(t);return{nodeCount:r,maxDepth:n,avgBranchingFactor:a,contentDistribution:s}}countNodes(t){let r=t.level>0?1:0;for(const n of t.children)r+=this.countNodes(n);return r}calculateMaxDepth(t){let r=t.level;for(const n of t.children)r=Math.max(r,this.calculateMaxDepth(n));return r}calculateBranchingFactor(t){const r=this.getAllNodes(t),n=r.reduce((a,s)=>a+s.children.length,0);return r.length>0?n/r.length:0}analyzeContentDistribution(t){const r={},n={low:0,medium:0,high:0},a={},s=o=>{if(o.level>0){r[o.level]=(r[o.level]||0)+1;const c=o.metadata.complexity;c&&c in n&&(n[c]=(n[c]||0)+1),o.metadata.hasCode&&(a.code=(a.code||0)+1),o.metadata.hasImages&&(a.images=(a.images||0)+1),o.metadata.hasTables&&(a.tables=(a.tables||0)+1),o.metadata.hasLists&&(a.lists=(a.lists||0)+1)}o.children.forEach(s)};return s(t),{byLevel:r,byComplexity:n,byContentType:a}}getAllNodes(t){const r=[];t.level>0&&r.push(t);for(const n of t.children)r.push(...this.getAllNodes(n));return r}getTotalWordCount(t){let r=t.metadata.wordCount;for(const n of t.children)r+=this.getTotalWordCount(n);return r}hasContentType(t,r){return t.metadata[r]?!0:t.children.some(n=>this.hasContentType(n,r))}getOverallComplexity(t){const r=this.getAllNodes(t),n={low:1,medium:2,high:3},a=r.reduce((s,o)=>s+n[o.metadata.complexity],0)/r.length;return a<1.5?"low":a<2.5?"medium":"high"}createEmptyMetadata(){return{title:"Empty Document",totalNodes:0,maxDepth:0,totalWordCount:0,estimatedReadTime:0,hasImages:!1,hasCode:!1,hasTables:!1,complexity:"low"}}createEmptyNodeMetadata(){return{wordCount:0,characterCount:0,hasImages:!1,hasLinks:!1,hasCode:!1,hasTables:!1,hasLists:!1,complexity:"low",estimatedReadTime:0}}}const Je=new Wa,Ha="http://localhost:3001",Za=3e4;class Z extends Error{constructor(t,r,n,a){super(t),this.code=r,this.statusCode=n,this.details=a,this.name="ApiError"}}class Ne extends Error{constructor(t="Network request failed"){super(t),this.name="NetworkError"}}class Nt extends Error{constructor(t="Request timeout"){super(t),this.name="TimeoutError"}}class qa{constructor(t=Ha,r=Za){D(this,"baseUrl");D(this,"timeout");this.baseUrl=t,this.timeout=r}async request(t,r={}){const n=`${this.baseUrl}${t}`,a=new AbortController,s=setTimeout(()=>a.abort(),this.timeout);try{const o=await fetch(n,{...r,signal:a.signal,headers:{"Content-Type":"application/json",...r.headers}});if(clearTimeout(s),!o.ok){let c;try{c=await o.json()}catch{c={message:o.statusText}}throw new Z(c.error?.message||c.message||"Request failed",c.error?.code||"HTTP_ERROR",o.status,c.error?.details)}return await o.json()}catch(o){throw clearTimeout(s),o instanceof Z?o:o instanceof DOMException&&o.name==="AbortError"?new Nt("Request timeout"):o instanceof TypeError&&o.message.includes("fetch")?new Ne("Network connection failed"):new Ne(o instanceof Error?o.message:"Unknown network error")}}async parseContent(t,r){const n=await this.request("/api/parse",{method:"POST",body:JSON.stringify({content:t,options:r})});if(!n.success||!n.data)throw new Z(n.error?.message||"Parse failed",n.error?.code||"PARSE_ERROR",500,n.error?.details);return n.data}async parseFile(t,r){const n=new FormData;n.append("file",t),r&&n.append("options",JSON.stringify(r));const a=await this.request("/api/parse/file",{method:"POST",body:n,headers:{}});if(!a.success||!a.data)throw new Z(a.error?.message||"File parse failed",a.error?.code||"PARSE_ERROR",500,a.error?.details);return a.data}async healthCheck(){return await this.request("/api/parse/health")}async isAvailable(){try{return(await this.healthCheck()).status==="ok"}catch{return!1}}}const ae=new qa,Ga=e=>e instanceof Z,Et=e=>e instanceof Ne,Ct=e=>e instanceof Nt,St=e=>Ga(e)?e.message:Et(e)?"Network connection failed. Please check your internet connection.":Ct(e)?"Request timeout. Please try again.":e instanceof Error?e.message:"An unexpected error occurred",Ka=!0,Ya={mode:"api",timeout:3e4,retries:1,fallbackEnabled:Ka};class Ja{constructor(t={}){D(this,"config");D(this,"lastUsedMode","local");this.config={...Ya,...t},this.lastUsedMode=this.config.mode==="local"?"local":"api"}async parseDocument(t,r){const n=Date.now();try{let a;switch(this.config.mode){case"local":a=await this.parseWithLocal(t,r),this.lastUsedMode="local";break;case"api":a=await this.parseWithAPI(t,r),this.lastUsedMode="api";break;case"auto":a=await this.parseWithAuto(t,r);break;default:throw new Error(`Invalid parser mode: ${this.config.mode}`)}const s=Date.now()-n;return console.log(`✅ Parsing completed using ${this.lastUsedMode} parser in ${s}ms`),a}catch(a){const s=Date.now()-n;throw console.error(`❌ Parsing failed after ${s}ms:`,a),a}}async parseFile(t,r){const n=Date.now();try{let a;switch(this.config.mode){case"local":a=await this.parseFileWithLocal(t,r),this.lastUsedMode="local";break;case"api":a=await this.parseFileWithAPI(t,r),this.lastUsedMode="api";break;case"auto":a=await this.parseFileWithAuto(t,r);break;default:throw new Error(`Invalid parser mode: ${this.config.mode}`)}const s=Date.now()-n;return console.log(`✅ File parsing completed using ${this.lastUsedMode} parser in ${s}ms`),a}catch(a){const s=Date.now()-n;throw console.error(`❌ File parsing failed after ${s}ms:`,a),a}}async parseWithLocal(t,r){return console.log("🔧 Using local parser..."),await Je.parseDocument(t,r)}async parseWithAPI(t,r){return console.log("🌐 Using API parser..."),await ae.parseContent(t,r)}async parseWithAuto(t,r){try{console.log("🌐 Trying API parser first...");const n=await this.parseWithAPI(t,r);return this.lastUsedMode="api",n}catch(n){if(this.config.fallbackEnabled&&this.shouldFallbackToLocal(n)){console.log("🔧 API failed, falling back to local parser...");const a=await this.parseWithLocal(t,r);return this.lastUsedMode="local",a}throw n}}async parseFileWithLocal(t,r){console.log("🔧 Using local parser for file...");const n=await t.text();return await Je.parseDocument(n,r)}async parseFileWithAPI(t,r){return console.log("🌐 Using API parser for file..."),await ae.parseFile(t,r)}async parseFileWithAuto(t,r){try{console.log("🌐 Trying API parser for file first...");const n=await this.parseFileWithAPI(t,r);return this.lastUsedMode="api",n}catch(n){if(this.config.fallbackEnabled&&this.shouldFallbackToLocal(n)){console.log("🔧 API failed, falling back to local parser for file...");const a=await this.parseFileWithLocal(t,r);return this.lastUsedMode="local",a}throw n}}shouldFallbackToLocal(t){return!!(Et(t)||Ct(t)||t instanceof Error&&t.message.includes("fetch"))}async isAPIAvailable(){try{return await ae.isAvailable()}catch{return!1}}getCurrentMode(){return this.config.mode}getLastUsedMode(){return this.lastUsedMode}updateConfig(t){this.config={...this.config,...t}}async getStatus(){const t=await this.isAPIAvailable();return{currentMode:this.config.mode,lastUsedMode:this.lastUsedMode,apiAvailable:t,localAvailable:!0,fallbackEnabled:this.config.fallbackEnabled,config:this.config}}}const Me=new Ja;function Xa({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3"}))}const Qa=m.forwardRef(Xa),ei=Qa;function ti({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))}const ri=m.forwardRef(ti),Xe=ri;function ni({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"}))}const ai=m.forwardRef(ni),K=ai;function ii({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))}const si=m.forwardRef(ii),Qe=si;function oi({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"}))}const li=m.forwardRef(oi),ci=li;function ui({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 9V4.5M9 9H4.5M9 9 3.75 3.75M9 15v4.5M9 15H4.5M9 15l-5.25 5.25M15 9h4.5M15 9V4.5M15 9l5.25-5.25M15 15h4.5M15 15v4.5m0-4.5 5.25 5.25"}))}const di=m.forwardRef(ui),hi=di;function mi({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 3.75v4.5m0-4.5h4.5m-4.5 0L9 9M3.75 20.25v-4.5m0 4.5h4.5m-4.5 0L9 15M20.25 3.75h-4.5m4.5 0v4.5m0-4.5L15 9m5.25 11.25h-4.5m4.5 0v-4.5m0 4.5L15 15"}))}const fi=m.forwardRef(mi),Lt=fi;function gi({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))}const xi=m.forwardRef(gi),pi=xi;function yi({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const bi=m.forwardRef(yi),q=bi;function wi({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"}))}const ki=m.forwardRef(wi),vi=ki;function ji({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))}const Ni=m.forwardRef(ji),Ei=Ni;function Ci({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const Si=m.forwardRef(Ci),At=Si;function Li({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 15a4.5 4.5 0 0 0 4.5 4.5H18a3.75 3.75 0 0 0 1.332-7.257 3 3 0 0 0-3.758-3.848 5.25 5.25 0 0 0-10.233 2.33A4.502 4.502 0 0 0 2.25 15Z"}))}const Ai=m.forwardRef(Li),et=Ai;function Ti({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"}),m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const Mi=m.forwardRef(Ti),Di=Mi;function Ii({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z"}))}const Fi=m.forwardRef(Ii),tt=Fi;function Ri({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m6.75 12-3-3m0 0-3 3m3-3v6m-1.5-15H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))}const zi=m.forwardRef(Ri),_i=zi;function Oi({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))}const $i=m.forwardRef(Oi),B=$i;function Pi({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}const Bi=m.forwardRef(Pi),ie=Bi;function Vi({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const Ui=m.forwardRef(Vi),Tt=Ui;function Wi({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5.25 8.25h15m-16.5 7.5h15m-1.8-13.5-3.9 19.5m-2.1-19.5-3.9 19.5"}))}const Hi=m.forwardRef(Wi),Mt=Hi;function Zi({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}const qi=m.forwardRef(Zi),Dt=qi;function Gi({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 12h14"}))}const Ki=m.forwardRef(Gi),Yi=Ki;function Ji({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"}))}const Xi=m.forwardRef(Ji),Qi=Xi;function es({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))}const ts=m.forwardRef(es),rs=ts;function ns({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M7.217 10.907a2.25 2.25 0 1 0 0 2.186m0-2.186c.18.324.283.696.283 1.093s-.103.77-.283 1.093m0-2.186 9.566-5.314m-9.566 7.5 9.566 5.314m0 0a2.25 2.25 0 1 0 3.935 2.186 2.25 2.25 0 0 0-3.935-2.186Zm0-12.814a2.25 2.25 0 1 0 3.933-2.185 2.25 2.25 0 0 0-3.933 2.185Z"}))}const as=m.forwardRef(ns),It=as;function is({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6A2.25 2.25 0 0 1 6 3.75h2.25A2.25 2.25 0 0 1 10.5 6v2.25a2.25 2.25 0 0 1-2.25 2.25H6a2.25 2.25 0 0 1-2.25-2.25V6ZM3.75 15.75A2.25 2.25 0 0 1 6 13.5h2.25a2.25 2.25 0 0 1 2.25 2.25V18a2.25 2.25 0 0 1-2.25 2.25H6A2.25 2.25 0 0 1 3.75 18v-2.25ZM13.5 6a2.25 2.25 0 0 1 2.25-2.25H18A2.25 2.25 0 0 1 20.25 6v2.25A2.25 2.25 0 0 1 18 10.5h-2.25a2.25 2.25 0 0 1-2.25-2.25V6ZM13.5 15.75a2.25 2.25 0 0 1 2.25-2.25H18a2.25 2.25 0 0 1 2.25 2.25V18A2.25 2.25 0 0 1 18 20.25h-2.25A2.25 2.25 0 0 1 13.5 18v-2.25Z"}))}const ss=m.forwardRef(is),os=ss;function ls({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"}))}const cs=m.forwardRef(ls),us=cs;function ds({title:e,titleId:t,...r},n){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}const hs=m.forwardRef(ds),J=hs,Ft=()=>{const{setDocument:e,setDocumentLoading:t,setDocumentError:r}=L(),[n,a]=m.useState(!1),s=m.useRef(null),o=m.useCallback(async b=>{const f=b[0];if(!f)return;const g=10*1024*1024,y=[".md",".markdown",".txt"];if(f.size>g){r("File size exceeds 10MB limit");return}const v="."+f.name.split(".").pop()?.toLowerCase();if(!y.includes(v)){r("Unsupported file type. Please use .md, .markdown, or .txt files");return}t(!0),r(null);try{const x=await Me.parseFile(f,{maxDepth:6,includeContent:!0,extractMetadata:!0});if(x.tree&&x.metadata){const p=await f.text();e(p,x.tree,x.metadata)}else throw new Error("Failed to parse document")}catch(x){console.error("File processing error:",x),r(St(x))}finally{t(!1)}},[e,t,r]),c=m.useCallback(b=>{b.preventDefault(),a(!1);const f=b.dataTransfer.files;f.length>0&&o(f)},[o]),u=m.useCallback(b=>{b.preventDefault(),a(!0)},[]),d=m.useCallback(b=>{b.preventDefault(),a(!1)},[]),h=m.useCallback(()=>{s.current?.click()},[]),l=m.useCallback(b=>{const f=b.target.files;f&&o(f),b.target.value=""},[o]);return i.jsxs(i.Fragment,{children:[i.jsx("input",{ref:s,type:"file",accept:".md,.markdown,.txt",onChange:l,className:"hidden","aria-label":"Upload markdown file"}),i.jsxs("button",{onClick:h,onDrop:c,onDragOver:u,onDragLeave:d,className:`
          btn btn-primary flex items-center space-x-2 transition-all duration-200
          ${n?"bg-primary-700 scale-105":""}
        `,title:"Upload markdown file",children:[i.jsx(_i,{className:"h-4 w-4"}),i.jsx("span",{className:"hidden sm:inline",children:"Upload"})]})]})},Rt=()=>{const{setDocument:e,setDocumentLoading:t,setDocumentError:r}=L(),[n,a]=m.useState(!1),[s,o]=m.useState(""),c=m.useCallback(async()=>{if(!s.trim()){r("Please enter some markdown content");return}t(!0),r(null);try{const d=await Me.parseDocument(s,{maxDepth:6,includeContent:!0,extractMetadata:!0});if(d.tree&&d.metadata)e(s,d.tree,d.metadata),a(!1),o("");else throw new Error("Failed to parse document")}catch(d){console.error("Content processing error:",d),r(St(d))}finally{t(!1)}},[s,e,t,r]),u=m.useCallback(()=>{a(!1),o(""),r(null)},[r]);return n?i.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:i.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-strong max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[i.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700",children:[i.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Enter Markdown Content"}),i.jsx("button",{onClick:u,className:"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700","aria-label":"Close",children:i.jsx(J,{className:"h-5 w-5"})})]}),i.jsxs("div",{className:"p-4",children:[i.jsxs("div",{className:"mb-4",children:[i.jsx("label",{htmlFor:"markdown-content",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Markdown Content"}),i.jsx("textarea",{id:"markdown-content",value:s,onChange:d=>o(d.target.value),placeholder:`# Your Markdown Here

## Section 1

Write your markdown content here...

### Subsection

- List item 1
- List item 2

\`\`\`javascript
console.log('Code blocks are supported!');
\`\`\``,className:"w-full h-96 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white font-mono text-sm resize-none"})]}),i.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400 mb-4",children:i.jsxs("p",{children:[i.jsx("strong",{children:"Tip:"})," You can paste markdown content from any source. Supports headings (H1-H6), lists, code blocks, tables, and more."]})}),i.jsxs("div",{className:"flex items-center justify-end space-x-3",children:[i.jsx("button",{onClick:u,className:"btn btn-ghost",children:"Cancel"}),i.jsx("button",{onClick:c,disabled:!s.trim(),className:"btn btn-primary",children:"Parse Content"})]})]})]})}):i.jsxs("button",{onClick:()=>a(!0),className:"btn btn-secondary flex items-center space-x-2",title:"Enter markdown text",children:[i.jsx(B,{className:"h-4 w-4"}),i.jsx("span",{className:"hidden sm:inline",children:"Text Input"})]})},ms=()=>{const{visualization:e,setVisualizationType:t}=L(),r=[{type:"tree",icon:i.jsx(It,{className:"h-4 w-4"}),label:"Tree View"},{type:"treemap",icon:i.jsx(os,{className:"h-4 w-4"}),label:"Treemap View"}];return i.jsx("div",{className:"flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1",children:r.map(n=>i.jsxs("button",{onClick:()=>t(n.type),className:`
            flex items-center space-x-1 px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200
            ${e.type===n.type?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"}
          `,title:n.label,children:[n.icon,i.jsx("span",{className:"hidden md:inline",children:n.label.split(" ")[0]})]},n.type))})},fs=()=>{const{ui:e,setTheme:t}=L(),r=()=>{const n=e.theme==="light"?"dark":"light";t(n)};return i.jsx("button",{onClick:r,className:"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors",title:`Switch to ${e.theme==="light"?"dark":"light"} mode`,children:e.theme==="light"?i.jsx(Qi,{className:"h-5 w-5"}):i.jsx(us,{className:"h-5 w-5"})})},gs=()=>{const[e,t]=m.useState("checking"),[r,n]=m.useState(null),a=async()=>{t("checking");try{const u=await ae.isAvailable();t(u?"online":"offline"),n(new Date)}catch{t("offline"),n(new Date)}};m.useEffect(()=>{a();const u=setInterval(a,3e4);return()=>clearInterval(u)},[]);const s=()=>{switch(e){case"online":return"text-green-500";case"offline":return"text-red-500";default:return"text-yellow-500"}},o=()=>{switch(e){case"online":return i.jsx(q,{className:"h-4 w-4"});case"offline":return i.jsx(ie,{className:"h-4 w-4"});default:return i.jsx(K,{className:"h-4 w-4 animate-spin"})}},c=()=>{switch(e){case"online":return"API Online";case"offline":return"API Offline";default:return"Checking..."}};return i.jsxs("div",{className:"flex items-center space-x-2 text-sm",children:[i.jsxs("div",{className:`flex items-center space-x-1 ${s()}`,children:[o(),i.jsx("span",{className:"hidden md:inline",children:c()})]}),r&&e!=="checking"&&i.jsx("button",{onClick:a,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",title:`Last checked: ${r.toLocaleTimeString()}`,children:i.jsx(K,{className:"h-3 w-3"})})]})},xs=()=>{const[e,t]=m.useState(null),[r,n]=m.useState(!1),[a,s]=m.useState(!1),o=async()=>{n(!0);try{const l=await Me.getStatus();t(l)}catch(l){console.error("Failed to get parser status:",l)}finally{n(!1)}};if(m.useEffect(()=>{o();const l=setInterval(o,3e4);return()=>clearInterval(l)},[]),!e)return i.jsxs("div",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[i.jsx(K,{className:"h-4 w-4 animate-spin"}),i.jsx("span",{className:"hidden md:inline",children:"Loading..."})]});const c=()=>{switch(e.lastUsedMode){case"local":return i.jsx(tt,{className:"h-4 w-4"});case"api":return i.jsx(et,{className:"h-4 w-4"});default:return i.jsx(Di,{className:"h-4 w-4"})}},u=()=>e.currentMode==="local"?"text-blue-500":e.currentMode==="api"&&e.apiAvailable?"text-green-500":e.currentMode==="auto"?e.apiAvailable?"text-green-500":"text-yellow-500":"text-red-500",d=()=>{const l=e.currentMode.charAt(0).toUpperCase()+e.currentMode.slice(1),b=e.lastUsedMode.charAt(0).toUpperCase()+e.lastUsedMode.slice(1);return e.currentMode==="auto"?`Auto (${b})`:l},h=()=>e.currentMode==="local"?i.jsx(q,{className:"h-3 w-3 text-blue-500"}):e.apiAvailable?i.jsx(q,{className:"h-3 w-3 text-green-500"}):i.jsx(ie,{className:"h-3 w-3 text-yellow-500"});return i.jsxs("div",{className:"relative",children:[i.jsxs("div",{className:`flex items-center space-x-2 text-sm cursor-pointer ${u()}`,onClick:()=>s(!a),title:"Click for parser details",children:[i.jsxs("div",{className:"flex items-center space-x-1",children:[c(),i.jsx("span",{className:"hidden md:inline",children:d()})]}),h(),r&&i.jsx(K,{className:"h-3 w-3 animate-spin"})]}),a&&i.jsx("div",{className:"absolute right-0 top-full mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 min-w-64 z-50",children:i.jsxs("div",{className:"space-y-3",children:[i.jsx("div",{className:"border-b border-gray-200 dark:border-gray-700 pb-2",children:i.jsx("h3",{className:"font-semibold text-gray-900 dark:text-white",children:"Parser Status"})}),i.jsxs("div",{className:"space-y-2 text-sm",children:[i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Current Mode:"}),i.jsx("span",{className:`font-medium ${u()}`,children:e.currentMode.charAt(0).toUpperCase()+e.currentMode.slice(1)})]}),i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Last Used:"}),i.jsxs("div",{className:"flex items-center space-x-1",children:[e.lastUsedMode==="local"?i.jsx(tt,{className:"h-3 w-3 text-blue-500"}):i.jsx(et,{className:"h-3 w-3 text-green-500"}),i.jsx("span",{className:"font-medium",children:e.lastUsedMode.charAt(0).toUpperCase()+e.lastUsedMode.slice(1)})]})]}),i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"API Available:"}),i.jsxs("div",{className:"flex items-center space-x-1",children:[e.apiAvailable?i.jsx(q,{className:"h-3 w-3 text-green-500"}):i.jsx(ie,{className:"h-3 w-3 text-red-500"}),i.jsx("span",{className:e.apiAvailable?"text-green-600":"text-red-600",children:e.apiAvailable?"Yes":"No"})]})]}),i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Local Parser:"}),i.jsxs("div",{className:"flex items-center space-x-1",children:[i.jsx(q,{className:"h-3 w-3 text-blue-500"}),i.jsx("span",{className:"text-blue-600",children:"Available"})]})]}),i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Fallback:"}),i.jsx("span",{className:e.fallbackEnabled?"text-green-600":"text-gray-600",children:e.fallbackEnabled?"Enabled":"Disabled"})]})]}),i.jsx("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-2",children:i.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400 space-y-1",children:[i.jsxs("div",{children:["• ",i.jsx("strong",{children:"Local:"})," Frontend processing, works offline"]}),i.jsxs("div",{children:["• ",i.jsx("strong",{children:"API:"})," Server-side processing, better performance"]}),i.jsxs("div",{children:["• ",i.jsx("strong",{children:"Auto:"})," API with local fallback"]})]})}),i.jsxs("div",{className:"flex justify-between items-center pt-2",children:[i.jsx("button",{onClick:o,disabled:r,className:"text-xs text-blue-600 hover:text-blue-700 disabled:opacity-50",children:r?"Refreshing...":"Refresh Status"}),i.jsx("button",{onClick:()=>s(!1),className:"text-xs text-gray-500 hover:text-gray-700",children:"Close"})]})]})})]})},ps=()=>{const{ui:e,document:t,toggleSidebar:r,toggleFullscreen:n,clearDocument:a}=L(),s=()=>{t.tree&&confirm("Are you sure you want to start a new document? Current work will be lost.")&&a()};return i.jsx("header",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 h-16 flex items-center px-4 lg:px-6",children:i.jsxs("div",{className:"flex items-center justify-between w-full",children:[i.jsxs("div",{className:"flex items-center space-x-4",children:[i.jsxs("div",{className:"flex items-center space-x-3",children:[i.jsx(B,{className:"h-8 w-8 text-primary-600"}),i.jsx("h1",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Markdown Visualizer"})]}),i.jsx("button",{onClick:r,className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-500","aria-label":"Toggle sidebar",children:i.jsx(pi,{className:"h-5 w-5"})})]}),i.jsx("div",{className:"hidden md:flex items-center space-x-4",children:t.metadata&&i.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[i.jsx("span",{className:"font-medium",children:t.metadata.totalNodes})," sections",i.jsx("span",{className:"mx-2",children:"•"}),i.jsx("span",{className:"font-medium",children:t.metadata.totalWordCount})," words",i.jsx("span",{className:"mx-2",children:"•"}),i.jsx("span",{className:"font-medium",children:t.metadata.estimatedReadTime})," min read"]})}),i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx(Ft,{}),i.jsx(Rt,{}),t.tree&&i.jsx("button",{onClick:s,className:"btn btn-ghost text-sm",title:"New document",children:"New"}),t.tree&&i.jsx(ms,{}),i.jsx("button",{onClick:n,className:"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-500",title:e.isFullscreen?"Exit fullscreen":"Enter fullscreen",children:e.isFullscreen?i.jsx(hi,{className:"h-5 w-5"}):i.jsx(Lt,{className:"h-5 w-5"})}),i.jsx(fs,{}),i.jsx(xs,{}),i.jsx(gs,{})]})]})})};var ys="Expected a function",rt=0/0,bs="[object Symbol]",ws=/^\s+|\s+$/g,ks=/^[-+]0x[0-9a-f]+$/i,vs=/^0b[01]+$/i,js=/^0o[0-7]+$/i,Ns=parseInt,Es=typeof X=="object"&&X&&X.Object===Object&&X,Cs=typeof self=="object"&&self&&self.Object===Object&&self,Ss=Es||Cs||Function("return this")(),Ls=Object.prototype,As=Ls.toString,Ts=Math.max,Ms=Math.min,ye=function(){return Ss.Date.now()};function Ds(e,t,r){var n,a,s,o,c,u,d=0,h=!1,l=!1,b=!0;if(typeof e!="function")throw new TypeError(ys);t=nt(t)||0,Ee(r)&&(h=!!r.leading,l="maxWait"in r,s=l?Ts(nt(r.maxWait)||0,t):s,b="trailing"in r?!!r.trailing:b);function f(j){var N=n,S=a;return n=a=void 0,d=j,o=e.apply(S,N),o}function g(j){return d=j,c=setTimeout(x,t),h?f(j):o}function y(j){var N=j-u,S=j-d,A=t-N;return l?Ms(A,s-S):A}function v(j){var N=j-u,S=j-d;return u===void 0||N>=t||N<0||l&&S>=s}function x(){var j=ye();if(v(j))return p(j);c=setTimeout(x,y(j))}function p(j){return c=void 0,b&&n?f(j):(n=a=void 0,o)}function k(){c!==void 0&&clearTimeout(c),d=0,n=u=a=c=void 0}function C(){return c===void 0?o:p(ye())}function E(){var j=ye(),N=v(j);if(n=arguments,a=this,u=j,N){if(c===void 0)return g(u);if(l)return c=setTimeout(x,t),f(u)}return c===void 0&&(c=setTimeout(x,t)),o}return E.cancel=k,E.flush=C,E}function Ee(e){var t=typeof e;return!!e&&(t=="object"||t=="function")}function Is(e){return!!e&&typeof e=="object"}function Fs(e){return typeof e=="symbol"||Is(e)&&As.call(e)==bs}function nt(e){if(typeof e=="number")return e;if(Fs(e))return rt;if(Ee(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=Ee(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=e.replace(ws,"");var r=vs.test(e);return r||js.test(e)?Ns(e.slice(2),r?2:8):ks.test(e)?rt:+e}var Rs=Ds;const zs=Ce(Rs),_s=()=>{const{ui:e,document:t,setSearchQuery:r,setSearchResults:n,openModal:a}=L(),[s,o]=m.useState(e.searchQuery),[c,u]=m.useState(!1),d=m.useMemo(()=>zs(x=>{if(r(x),x.trim()&&t.tree){const p=h(t.tree,x);n(p),u(!0)}else n([]),u(!1)},300),[t.tree,r,n]),h=m.useCallback((x,p)=>{const k=[],C=p.toLowerCase(),E=j=>{if(j.title.toLowerCase().includes(C)&&k.push({nodeId:j.id,title:j.title,content:j.content.substring(0,150)+"...",relevance:l(j.title,C,"title"),type:"heading"}),j.content.toLowerCase().includes(C)){const N=b(j.content,C);k.push({nodeId:j.id,title:j.title,content:N,relevance:l(j.content,C,"content"),type:"content"})}j.children.forEach(E)};return x.children&&x.children.forEach(E),k.sort((j,N)=>N.relevance-j.relevance).slice(0,10)},[]),l=(x,p,k)=>{const C=x.toLowerCase(),E=p.toLowerCase();let j=0;C.includes(E)&&(j+=10),k==="title"&&(j+=5);const N=C.indexOf(E);return N!==-1&&(j+=Math.max(0,5-N/10)),j},b=(x,p)=>{const k=x.toLowerCase(),C=p.toLowerCase(),E=k.indexOf(C);if(E===-1)return x.substring(0,150)+"...";const j=Math.max(0,E-50),N=Math.min(x.length,E+p.length+50);let S=x.substring(j,N);return j>0&&(S="..."+S),N<x.length&&(S=S+"..."),S},f=m.useCallback(x=>{const p=x.target.value;o(p),d(p)},[d]),g=m.useCallback(()=>{o(""),r(""),n([]),u(!1)},[r,n]),y=m.useCallback(x=>{a(x.nodeId),u(!1)},[a]),v=m.useCallback(x=>{x.key==="Escape"&&u(!1)},[]);return i.jsxs("div",{className:"relative",children:[i.jsxs("div",{className:"relative",children:[i.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:i.jsx(Dt,{className:"h-4 w-4 text-gray-400"})}),i.jsx("input",{type:"text",value:s,onChange:f,onKeyDown:v,onFocus:()=>s&&u(!0),onBlur:()=>setTimeout(()=>u(!1),200),placeholder:"Search document...",className:"input pl-10 pr-10 text-sm","data-search-input":!0}),s&&i.jsx("button",{onClick:g,className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:i.jsx(J,{className:"h-4 w-4 text-gray-400 hover:text-gray-600"})})]}),c&&e.searchResults.length>0&&i.jsx("div",{className:"absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg max-h-64 overflow-y-auto",children:e.searchResults.map((x,p)=>i.jsx("button",{onClick:()=>y(x),className:"w-full text-left px-3 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-100 dark:border-gray-600 last:border-b-0",children:i.jsxs("div",{className:"flex items-start space-x-2",children:[i.jsx("div",{className:"flex-shrink-0 mt-1",children:x.type==="heading"?i.jsx("div",{className:"w-2 h-2 bg-primary-500 rounded-full"}):i.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full"})}),i.jsxs("div",{className:"flex-1 min-w-0",children:[i.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:x.title}),i.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 line-clamp-2",children:x.content})]})]})},`${x.nodeId}-${p}`))}),c&&s&&e.searchResults.length===0&&i.jsx("div",{className:"absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg p-3",children:i.jsxs("div",{className:"text-sm text-gray-500 dark:text-gray-400 text-center",children:['No results found for "',s,'"']})})]})},Os=()=>{const{document:e,visualization:t,openModal:r,setSelectedNode:n}=L(),[a,s]=m.useState(new Set),o=m.useCallback(d=>{s(h=>{const l=new Set(h);return l.has(d)?l.delete(d):l.add(d),l})},[]),c=m.useCallback(d=>{n(d),r(d)},[n,r]),u=m.useCallback((d,h=0)=>{const l=d.children.length>0,b=a.has(d.id),f=d.id===t.selectedNode,g=d.id===t.hoveredNode;return d.level===0?i.jsx("div",{children:d.children.map(y=>u(y,h))},d.id):i.jsxs("div",{className:"toc-item",children:[i.jsxs("div",{className:`
            flex items-center space-x-2 px-2 py-1.5 rounded-md cursor-pointer transition-colors
            ${f?"bg-primary-100 dark:bg-primary-900 text-primary-900 dark:text-primary-100":g?"bg-gray-100 dark:bg-gray-700":"hover:bg-gray-50 dark:hover:bg-gray-800"}
          `,style:{paddingLeft:`${h*12+8}px`},children:[l?i.jsx("button",{onClick:y=>{y.stopPropagation(),o(d.id)},className:"flex-shrink-0 p-0.5 rounded hover:bg-gray-200 dark:hover:bg-gray-600","aria-label":b?"Collapse":"Expand",children:b?i.jsx(vi,{className:"h-3 w-3 text-gray-500"}):i.jsx(Ei,{className:"h-3 w-3 text-gray-500"})}):i.jsx("div",{className:"w-4 h-4 flex-shrink-0"}),i.jsxs("button",{onClick:()=>c(d.id),className:"flex-1 flex items-center space-x-2 text-left min-w-0",children:[i.jsx("div",{className:`
              w-2 h-2 rounded-full flex-shrink-0
              ${d.level===1?"bg-blue-500":d.level===2?"bg-green-500":d.level===3?"bg-yellow-500":d.level===4?"bg-red-500":d.level===5?"bg-purple-500":"bg-gray-500"}
            `}),i.jsx("span",{className:`
              text-sm truncate
              ${f?"font-medium":"text-gray-700 dark:text-gray-300"}
            `,children:d.title}),i.jsxs("span",{className:"text-xs text-gray-400 dark:text-gray-500 flex-shrink-0",children:[d.metadata.wordCount,"w"]})]})]}),l&&b&&i.jsx("div",{className:"mt-1",children:d.children.map(y=>u(y,h+1))})]},d.id)},[a,t.selectedNode,t.hoveredNode,o,c]);return e.tree?i.jsxs("div",{className:"space-y-1",children:[e.tree.children.map(d=>(!a.has(d.id)&&d.children.length>0&&s(h=>new Set([...h,d.id])),u(d))),e.tree.children.length===0&&i.jsx("div",{className:"text-center py-4",children:i.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"No headings found in document"})})]}):i.jsxs("div",{className:"text-center py-8",children:[i.jsx(B,{className:"h-8 w-8 text-gray-400 dark:text-gray-500 mx-auto mb-2"}),i.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"No table of contents available"})]})},$s=()=>{const{document:e}=L();if(!e.metadata)return null;const t=[{icon:i.jsx(Mt,{className:"h-4 w-4"}),label:"Sections",value:e.metadata.totalNodes.toString(),color:"text-blue-600 dark:text-blue-400"},{icon:i.jsx(B,{className:"h-4 w-4"}),label:"Words",value:e.metadata.totalWordCount.toLocaleString(),color:"text-green-600 dark:text-green-400"},{icon:i.jsx(At,{className:"h-4 w-4"}),label:"Read Time",value:`${e.metadata.estimatedReadTime} min`,color:"text-purple-600 dark:text-purple-400"},{icon:i.jsx(Tt,{className:"h-4 w-4"}),label:"Max Depth",value:`H${e.metadata.maxDepth}`,color:"text-orange-600 dark:text-orange-400"}],r={low:"text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900",medium:"text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900",high:"text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900"};return i.jsxs("div",{className:"space-y-4",children:[i.jsx("div",{className:"grid grid-cols-2 gap-3",children:t.map((n,a)=>i.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3",children:[i.jsxs("div",{className:"flex items-center space-x-2 mb-1",children:[i.jsx("div",{className:n.color,children:n.icon}),i.jsx("span",{className:"text-xs text-gray-600 dark:text-gray-400",children:n.label})]}),i.jsx("div",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:n.value})]},a))}),i.jsxs("div",{className:"space-y-2",children:[i.jsx("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Document Features"}),i.jsxs("div",{className:"flex flex-wrap gap-2",children:[i.jsxs("span",{className:`
            inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
            ${r[e.metadata.complexity]}
          `,children:[e.metadata.complexity," complexity"]}),e.metadata.hasCode&&i.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700",children:"Code blocks"}),e.metadata.hasImages&&i.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700",children:"Images"}),e.metadata.hasTables&&i.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700",children:"Tables"})]})]}),i.jsxs("div",{className:"pt-2 border-t border-gray-200 dark:border-gray-600",children:[i.jsx("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Document Title"}),i.jsx("p",{className:"text-sm text-gray-900 dark:text-white font-medium",children:e.metadata.title})]})]})},Ps=()=>{const{ui:e,document:t,toggleSidebar:r}=L();return e.sidebarOpen?i.jsxs(i.Fragment,{children:[i.jsx("div",{className:"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-20",onClick:r}),i.jsx("aside",{className:`
        fixed lg:relative inset-y-0 left-0 z-30 w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700
        transform transition-transform duration-300 ease-in-out lg:transform-none
        ${e.sidebarOpen?"translate-x-0":"-translate-x-full lg:translate-x-0"}
      `,children:i.jsxs("div",{className:"flex flex-col h-full",children:[i.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700",children:[i.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Document Explorer"}),i.jsx("button",{onClick:r,className:"lg:hidden p-1 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700","aria-label":"Close sidebar",children:i.jsx(J,{className:"h-5 w-5"})})]}),i.jsx("div",{className:"flex-1 overflow-y-auto",children:t.tree?i.jsxs("div",{className:"p-4 space-y-6",children:[i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Search Document"}),i.jsx(_s,{})]}),i.jsx($s,{}),i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Table of Contents"}),i.jsx(Os,{})]})]}):i.jsx("div",{className:"p-4",children:i.jsxs("div",{className:"text-center py-12",children:[i.jsx("div",{className:"text-gray-400 dark:text-gray-500 mb-4",children:i.jsx("svg",{className:"mx-auto h-12 w-12",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),i.jsx("h3",{className:"text-sm font-medium text-gray-900 dark:text-white mb-2",children:"No document loaded"}),i.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Upload a markdown file to get started"})]})})})]})})]}):null};class Bs{constructor(){D(this,"svg",null);D(this,"g",null);D(this,"zoom",null);D(this,"container",null);D(this,"config",null);D(this,"eventHandlers",new Map)}render(t,r,n){switch(this.container=r,this.config=n,ue(r).selectAll("*").remove(),this.initializeSVG(r,n),n.layout==="tree"?this.selectOptimalLayout(t):n.layout){case"treemap":this.renderTreemap(t,n);break;default:this.renderTree(t,n);break}}selectOptimalLayout(t){const r=this.analyzeTree(t);return r.nodeCount<=50&&r.maxDepth<=4?"radial-tree":r.nodeCount<=100&&r.maxDepth<=6?"vertical-tree":r.nodeCount<=200?"horizontal-tree":"treemap"}analyzeTree(t){const r=this.countNodes(t),n=this.calculateMaxDepth(t),a=this.calculateBranchingFactor(t);return{nodeCount:r,maxDepth:n,avgBranchingFactor:a}}countNodes(t){let r=t.level>0?1:0;for(const n of t.children)r+=this.countNodes(n);return r}calculateMaxDepth(t){let r=t.level;for(const n of t.children)r=Math.max(r,this.calculateMaxDepth(n));return r}calculateBranchingFactor(t){const r=this.getAllNodes(t),n=r.reduce((a,s)=>a+s.children.length,0);return r.length>0?n/r.length:0}getAllNodes(t){const r=[];t.level>0&&r.push(t);for(const n of t.children)r.push(...this.getAllNodes(n));return r}initializeSVG(t,r){this.svg=ue(t).append("svg").attr("class","visualization-svg").attr("width",r.width).attr("height",r.height).style("background",r.nodeColors.background),this.g=this.svg.append("g").attr("class","visualization-group"),r.enableZoom&&this.setupZoom()}setupZoom(){!this.svg||!this.g||(this.zoom=Yt().scaleExtent([.1,3]).on("zoom",t=>{this.g.attr("transform",t.transform),this.emit("zoom",{type:"zoom",scale:t.transform.k,position:{x:t.transform.x,y:t.transform.y},originalEvent:t.sourceEvent})}),this.svg.call(this.zoom))}renderTree(t,r){if(!this.g)return;const n=_e(t),s=this.createTreeLayout(r)(n);this.renderNodes(s.descendants(),r),this.renderLinks(s.links(),r)}createTreeLayout(t){const{width:r,height:n,orientation:a,padding:s}=t,o=r-s.left-s.right,c=n-s.top-s.bottom;switch(a){case"radial":return de().size([2*Math.PI,Math.min(o,c)/2-50]).separation((u,d)=>(u.parent===d.parent?1:2)/(u.depth||1));case"horizontal":return de().size([c,o]);default:return de().size([o,c])}}renderNodes(t,r){if(!this.g)return;const n=this.g.selectAll(".node").data(t,s=>s.data.id),a=n.enter().append("g").attr("class","node").attr("transform",s=>this.getNodeTransform(s,r.orientation));a.append("circle").attr("r",s=>this.calculateNodeSize(s.data,r.nodeSize)).attr("fill",s=>this.getNodeColor(s.data,r.nodeColors)).attr("stroke",r.nodeColors.primary).attr("stroke-width",2).style("cursor","pointer"),a.append("text").attr("dy",".35em").attr("text-anchor",s=>this.getTextAnchor(s,r.orientation)).attr("x",s=>this.getTextOffset(s,r.orientation)).text(s=>this.truncateText(s.data.title,20)).style("font-size","12px").style("fill",r.nodeColors.text).style("pointer-events","none"),a.on("click",(s,o)=>this.handleNodeClick(o.data.id,s)).on("mouseenter",(s,o)=>this.handleNodeHover(o.data.id,s)).on("mouseleave",s=>this.handleNodeHover(null,s)),n.transition().duration(r.animationDuration).attr("transform",s=>this.getNodeTransform(s,r.orientation)),n.exit().transition().duration(r.animationDuration).style("opacity",0).remove()}renderLinks(t,r){if(!this.g)return;const n=this.g.selectAll(".link").data(t,a=>a.target.data.id);n.enter().append("path").attr("class","link").attr("fill","none").attr("stroke",r.nodeColors.secondary).attr("stroke-width",1.5).attr("d",a=>this.getLinkPath(a,r.orientation)),n.transition().duration(r.animationDuration).attr("d",a=>this.getLinkPath(a,r.orientation)),n.exit().transition().duration(r.animationDuration).style("opacity",0).remove()}renderTreemap(t,r){if(!this.g)return;const n=_e(t).sum(o=>this.getNodeValue(o,r.nodeSize.sizeMetric)).sort((o,c)=>(c.value||0)-(o.value||0)),s=Jt().size([r.width,r.height]).padding(2).round(!0)(n);this.renderTreemapNodes(s.leaves(),r)}renderTreemapNodes(t,r){if(!this.g)return;const a=this.g.selectAll(".treemap-node").data(t,s=>s.data.id).enter().append("g").attr("class","treemap-node");a.append("rect").attr("x",s=>s.x0).attr("y",s=>s.y0).attr("width",s=>s.x1-s.x0).attr("height",s=>s.y1-s.y0).attr("fill",s=>this.getTreemapNodeColor(s.data,r.nodeColors)).attr("stroke",r.nodeColors.background).attr("stroke-width",1).style("cursor","pointer"),a.append("text").attr("x",s=>(s.x0+s.x1)/2).attr("y",s=>(s.y0+s.y1)/2).attr("text-anchor","middle").attr("dominant-baseline","middle").text(s=>this.getTreemapLabel(s)).style("font-size",s=>this.getTreemapFontSize(s)).style("fill",r.nodeColors.text).style("pointer-events","none"),a.on("click",(s,o)=>this.handleNodeClick(o.data.id,s)).on("mouseenter",(s,o)=>this.handleNodeHover(o.data.id,s)).on("mouseleave",s=>this.handleNodeHover(null,s))}getNodeTransform(t,r){if(r==="radial"){const n=t.x,a=t.y,s=Math.cos(n-Math.PI/2)*a,o=Math.sin(n-Math.PI/2)*a;return`translate(${s},${o})`}else return r==="horizontal"?`translate(${t.y},${t.x})`:`translate(${t.x},${t.y})`}calculateNodeSize(t,r){if(!r.sizeByContent)return r.baseSize;const n=this.getNodeValue(t,r.sizeMetric),a=Xt().domain([0,1e3]).range([r.minSize,r.maxSize]);return Math.max(r.minSize,Math.min(r.maxSize,a(n)))}getNodeValue(t,r){switch(r){case"wordCount":return t.metadata.wordCount;case"characterCount":return t.metadata.characterCount;case"childCount":return t.children.length;default:return 1}}getNodeColor(t,r){const n=Math.min(t.level-1,r.levels.length-1);return r.levels[n]||r.primary}getTreemapNodeColor(t,r){const n=Math.min(t.level-1,r.levels.length-1);return r.levels[n]||r.primary}getTextAnchor(t,r){return r==="horizontal"?t.children?"end":"start":"middle"}getTextOffset(t,r){return r==="horizontal"?t.children?-10:10:0}getLinkPath(t,r){return r==="radial"?Qt().angle(n=>n.x).radius(n=>n.y)(t):r==="horizontal"?er().x(n=>n.y).y(n=>n.x)(t):tr().x(n=>n.x).y(n=>n.y)(t)}getTreemapLabel(t){const r=t.x1-t.x0,n=t.y1-t.y0;return r*n<1e3?"":this.truncateText(t.data.title,Math.floor(r/8))}getTreemapFontSize(t){const r=t.x1-t.x0,n=t.y1-t.y0,a=r*n;return a<1e3?"0px":a<5e3?"10px":a<1e4?"12px":"14px"}truncateText(t,r){return t.length<=r?t:t.substring(0,r-3)+"..."}handleNodeClick(t,r){this.emit("nodeClick",{type:"click",nodeId:t,originalEvent:r})}handleNodeHover(t,r){this.emit("nodeHover",{type:"hover",nodeId:t||void 0,originalEvent:r})}on(t,r){this.eventHandlers.has(t)||this.eventHandlers.set(t,[]),this.eventHandlers.get(t).push(r)}off(t,r){const n=this.eventHandlers.get(t);if(n){const a=n.indexOf(r);a>-1&&n.splice(a,1)}}emit(t,r){const n=this.eventHandlers.get(t);n&&n.forEach(a=>a(r))}updateConfig(t){this.config&&(this.config={...this.config,...t})}getConfig(){return this.config}updateLayout(t,r=!0){this.config&&(this.config.layout=t)}handleZoom(t,r){if(this.zoom&&this.svg){const n=rr.scale(t);r&&n.translate(r.x,r.y),this.svg.transition().duration(300).call(this.zoom.transform,n)}}destroy(){this.eventHandlers.clear(),this.container&&ue(this.container).selectAll("*").remove()}}const _=new Bs,Vs=({zoomLevel:e,minZoom:t=.1,maxZoom:r=3,onZoomIn:n,onZoomOut:a,onResetZoom:s,onFitToScreen:o})=>{const c=u=>`${Math.round(u*100)}%`;return i.jsxs("div",{className:"flex flex-col bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden",children:[i.jsx("button",{onClick:n,disabled:e>=r,className:"p-2 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed border-b border-gray-200 dark:border-gray-700",title:"Zoom in","aria-label":"Zoom in",children:i.jsx(rs,{className:"h-4 w-4 text-gray-600 dark:text-gray-400"})}),i.jsx("div",{className:"px-3 py-2 text-xs font-medium text-gray-600 dark:text-gray-400 text-center border-b border-gray-200 dark:border-gray-700 min-w-[60px]",children:c(e)}),i.jsx("button",{onClick:a,disabled:e<=t,className:"p-2 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed border-b border-gray-200 dark:border-gray-700",title:"Zoom out","aria-label":"Zoom out",children:i.jsx(Yi,{className:"h-4 w-4 text-gray-600 dark:text-gray-400"})}),i.jsx("button",{onClick:o,className:"p-2 hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-200 dark:border-gray-700",title:"Fit to screen","aria-label":"Fit to screen",children:i.jsx(Lt,{className:"h-4 w-4 text-gray-600 dark:text-gray-400"})}),i.jsx("button",{onClick:s,className:"p-2 hover:bg-gray-50 dark:hover:bg-gray-700",title:"Reset zoom (100%)","aria-label":"Reset zoom",children:i.jsx(K,{className:"h-4 w-4 text-gray-600 dark:text-gray-400"})})]})};function zt(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(r=zt(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function Us(){for(var e,t,r=0,n="",a=arguments.length;r<a;r++)(e=arguments[r])&&(t=zt(e))&&(n&&(n+=" "),n+=t);return n}const _t=({size:e="medium",color:t,className:r})=>i.jsxs("div",{className:Us("loading-spinner",`loading-spinner--${e}`,r),style:{color:t},role:"status","aria-label":"Loading",children:[i.jsxs("svg",{className:"animate-spin",viewBox:"0 0 24 24",children:[i.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",fill:"none",stroke:"currentColor",strokeWidth:"2"}),i.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),i.jsx("span",{className:"sr-only",children:"Loading..."})]}),Ws=()=>{const e=m.useRef(null),{document:t,visualization:r,updateVisualizationConfig:n,setSelectedNode:a,setHoveredNode:s,openModal:o,setZoomLevel:c}=L(),u=m.useCallback(()=>{if(e.current){const{width:f,height:g}=e.current.getBoundingClientRect();n({width:f,height:g})}},[n]);m.useEffect(()=>{const f=new ResizeObserver(u);return e.current&&f.observe(e.current),()=>f.disconnect()},[u]),m.useEffect(()=>{u()},[u]),m.useEffect(()=>{if(!(!t.tree||!e.current)){try{_.on("nodeClick",f=>{f.nodeId&&(a(f.nodeId),o(f.nodeId))}),_.on("nodeHover",f=>{s(f.nodeId)}),_.on("zoom",f=>{f.scale&&c(f.scale)}),_.render(t.tree,e.current,r.config)}catch(f){console.error("Visualization render error:",f)}return()=>{_.destroy()}}},[t.tree,r.config,a,s,o,c]);const d=m.useCallback(()=>{const f=Math.min(r.zoomLevel*1.2,3);_.handleZoom(f)},[r.zoomLevel]),h=m.useCallback(()=>{const f=Math.max(r.zoomLevel/1.2,.1);_.handleZoom(f)},[r.zoomLevel]),l=m.useCallback(()=>{_.handleZoom(1)},[]),b=m.useCallback(()=>{if(e.current&&t.tree){const f=e.current.getBoundingClientRect(),g=Math.min(f.width/r.config.width,f.height/r.config.height)*.9;_.handleZoom(Math.max(.1,Math.min(3,g)))}},[r.config,t.tree]);return t.tree?i.jsxs("div",{className:"flex-1 relative bg-white dark:bg-gray-900",children:[i.jsx("div",{ref:e,className:"w-full h-full visualization-container",role:"img","aria-label":"Document structure visualization",children:t.isLoading&&i.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-white/80 dark:bg-gray-900/80 z-10",children:i.jsxs("div",{className:"flex items-center space-x-3",children:[i.jsx(_t,{size:"medium"}),i.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Rendering visualization..."})]})})}),i.jsx("div",{className:"absolute bottom-4 right-4 z-10",children:i.jsx(Vs,{zoomLevel:r.zoomLevel,onZoomIn:d,onZoomOut:h,onResetZoom:l,onFitToScreen:b})}),i.jsx("div",{className:"absolute top-4 left-4 z-10",children:i.jsx("div",{className:"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg px-3 py-2 text-sm",children:i.jsxs("div",{className:"text-gray-600 dark:text-gray-400",children:[i.jsx("span",{className:"font-medium",children:r.type})," view",r.selectedNode&&i.jsxs(i.Fragment,{children:[i.jsx("span",{className:"mx-2",children:"•"}),i.jsx("span",{children:"Node selected"})]})]})})}),i.jsx("div",{className:"absolute bottom-4 left-4 z-10",children:i.jsx("div",{className:"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg px-3 py-2 text-xs text-gray-500 dark:text-gray-400",children:"Click nodes to view content • Use mouse wheel to zoom • Drag to pan"})})]}):i.jsx("div",{className:"flex-1 flex items-center justify-center",children:i.jsxs("div",{className:"text-center",children:[i.jsx("div",{className:"text-gray-400 dark:text-gray-500 mb-4",children:i.jsx("svg",{className:"mx-auto h-12 w-12",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}),i.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No visualization available"}),i.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"Upload a document to see the visualization"})]})})},Hs=()=>{const e=[{icon:i.jsx(B,{className:"h-8 w-8"}),title:"Upload or Paste Markdown",description:"Upload files or paste text directly. Server-side processing for optimal performance."},{icon:i.jsx(It,{className:"h-8 w-8"}),title:"Interactive Visualization",description:"Explore your document structure with tree and treemap views"},{icon:i.jsx(Dt,{className:"h-8 w-8"}),title:"Smart Search",description:"Find content quickly with full-text search and navigation"},{icon:i.jsx(Tt,{className:"h-8 w-8"}),title:"Rich Content Display",description:"View formatted content with syntax highlighting and math support"}];return i.jsx("div",{className:"flex-1 flex items-center justify-center p-8",children:i.jsxs("div",{className:"max-w-4xl w-full text-center",children:[i.jsxs("div",{className:"mb-12",children:[i.jsx("div",{className:"flex justify-center mb-6",children:i.jsx("div",{className:"p-4 bg-primary-100 dark:bg-primary-900 rounded-full",children:i.jsx(B,{className:"h-16 w-16 text-primary-600 dark:text-primary-400"})})}),i.jsx("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"Markdown Visualizer"}),i.jsx("p",{className:"text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto",children:"Transform your markdown documents into interactive visual explorations. Understand document structure at a glance and navigate content effortlessly."}),i.jsxs("div",{className:"flex justify-center space-x-4",children:[i.jsx(Ft,{}),i.jsx(Rt,{})]})]}),i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12",children:e.map((t,r)=>i.jsxs("div",{className:"text-center",children:[i.jsx("div",{className:"flex justify-center mb-4",children:i.jsx("div",{className:"p-3 bg-gray-100 dark:bg-gray-800 rounded-lg text-primary-600 dark:text-primary-400",children:t.icon})}),i.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:t.title}),i.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:t.description})]},r))}),i.jsxs("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-8",children:[i.jsx("h2",{className:"text-2xl font-semibold text-gray-900 dark:text-white mb-4",children:"Getting Started"}),i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 text-left",children:[i.jsxs("div",{className:"flex items-start space-x-3",children:[i.jsx("div",{className:"flex-shrink-0 w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-semibold",children:"1"}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-medium text-gray-900 dark:text-white mb-1",children:"Upload Document"}),i.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Click the upload button or drag and drop your markdown file"})]})]}),i.jsxs("div",{className:"flex items-start space-x-3",children:[i.jsx("div",{className:"flex-shrink-0 w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-semibold",children:"2"}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-medium text-gray-900 dark:text-white mb-1",children:"Explore Structure"}),i.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Navigate the visual representation of your document hierarchy"})]})]}),i.jsxs("div",{className:"flex items-start space-x-3",children:[i.jsx("div",{className:"flex-shrink-0 w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-semibold",children:"3"}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-medium text-gray-900 dark:text-white mb-1",children:"Read Content"}),i.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Click on any node to view the formatted content in detail"})]})]})]})]}),i.jsx("div",{className:"mt-8 text-sm text-gray-500 dark:text-gray-400",children:i.jsx("p",{children:"Don't have a markdown file? Try uploading a README.md from your favorite project or create a simple document with headings and content."})})]})})},Zs=({error:e})=>{const{setDocumentError:t}=L(),r=()=>{t(null)};return i.jsx("div",{className:"bg-red-50 dark:bg-red-900/20 border-l-4 border-red-400 p-4 m-4 rounded-r-md",children:i.jsxs("div",{className:"flex",children:[i.jsx("div",{className:"flex-shrink-0",children:i.jsx(ie,{className:"h-5 w-5 text-red-400"})}),i.jsxs("div",{className:"ml-3 flex-1",children:[i.jsx("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:"Error Processing Document"}),i.jsx("div",{className:"mt-2 text-sm text-red-700 dark:text-red-300",children:i.jsx("p",{children:e})})]}),i.jsx("div",{className:"ml-auto pl-3",children:i.jsx("div",{className:"-mx-1.5 -my-1.5",children:i.jsxs("button",{onClick:r,className:"inline-flex rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-red-600 focus:ring-offset-2 focus:ring-offset-red-50",children:[i.jsx("span",{className:"sr-only",children:"Dismiss"}),i.jsx(J,{className:"h-5 w-5"})]})})})]})})},qs=()=>{const{document:e,ui:t}=L();return i.jsxs("main",{className:`
      flex-1 flex flex-col overflow-hidden
      ${t.sidebarOpen?"lg:ml-0":""}
    `,children:[e.error&&i.jsx(Zs,{error:e.error}),i.jsx("div",{className:"flex-1 relative",children:e.tree?i.jsx(Ws,{}):i.jsx(Hs,{})})]})},Gs=({node:e})=>{const t=r=>{if(!r.trim())return i.jsx("div",{className:"text-center py-12 text-gray-500 dark:text-gray-400",children:i.jsx("p",{children:"No content available for this section."})});let n=r.replace(/^### (.*$)/gim,'<h3 class="text-lg font-semibold text-gray-900 dark:text-white mt-6 mb-3">$1</h3>').replace(/^## (.*$)/gim,'<h2 class="text-xl font-semibold text-gray-900 dark:text-white mt-8 mb-4">$1</h2>').replace(/^# (.*$)/gim,'<h1 class="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4">$1</h1>').replace(/```(\w+)?\n([\s\S]*?)```/g,'<pre class="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto my-4"><code class="text-sm">$2</code></pre>').replace(/`([^`]+)`/g,'<code class="bg-gray-100 dark:bg-gray-800 px-1.5 py-0.5 rounded text-sm font-mono">$1</code>').replace(/\*\*(.*?)\*\*/g,'<strong class="font-semibold">$1</strong>').replace(/\*(.*?)\*/g,'<em class="italic">$1</em>').replace(/\[([^\]]+)\]\(([^)]+)\)/g,'<a href="$2" class="text-primary-600 dark:text-primary-400 hover:underline" target="_blank" rel="noopener noreferrer">$1</a>').replace(/!\[([^\]]*)\]\(([^)]+)\)/g,'<img src="$2" alt="$1" class="max-w-full h-auto rounded-lg my-4" />').replace(/^\* (.+)$/gm,'<li class="ml-4">$1</li>').replace(/(<li class="ml-4">.*<\/li>)/s,'<ul class="list-disc list-inside space-y-1 my-4">$1</ul>').replace(/^\d+\. (.+)$/gm,'<li class="ml-4">$1</li>').replace(/^> (.+)$/gm,'<blockquote class="border-l-4 border-gray-300 dark:border-gray-600 pl-4 italic text-gray-700 dark:text-gray-300 my-4">$1</blockquote>').replace(/\n\n/g,'</p><p class="mb-4">').replace(/^/,'<p class="mb-4">').replace(/$/,"</p>");return i.jsx("div",{className:"prose prose-gray dark:prose-invert max-w-none",dangerouslySetInnerHTML:{__html:n}})};return i.jsx("div",{className:"flex-1 overflow-y-auto p-6",id:"modal-content",children:i.jsxs("div",{className:"max-w-4xl mx-auto",children:[t(e.content),e.children.length>0&&i.jsxs("div",{className:"mt-12 pt-8 border-t border-gray-200 dark:border-gray-700",children:[i.jsxs("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:["Subsections (",e.children.length,")"]}),i.jsx("div",{className:"grid gap-4",children:e.children.map(r=>i.jsx("div",{className:"p-4 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors cursor-pointer",onClick:()=>{console.log("Navigate to child:",r.id)},children:i.jsxs("div",{className:"flex items-start justify-between",children:[i.jsxs("div",{className:"flex-1",children:[i.jsx("h4",{className:"font-medium text-gray-900 dark:text-white mb-1",children:r.title}),i.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400 line-clamp-2",children:[r.content.substring(0,150),"..."]})]}),i.jsxs("div",{className:"ml-4 text-xs text-gray-500 dark:text-gray-400",children:["H",r.level," • ",r.metadata.wordCount,"w"]})]})},r.id))})]})]})})},Ks=({node:e,breadcrumbs:t})=>{const{openModal:r}=L(),n=s=>{s!==e.id&&r(s)},a=s=>{const o=["text-blue-600 dark:text-blue-400","text-green-600 dark:text-green-400","text-yellow-600 dark:text-yellow-400","text-red-600 dark:text-red-400","text-purple-600 dark:text-purple-400","text-gray-600 dark:text-gray-400"];return o[Math.min(s-1,o.length-1)]};return i.jsxs("div",{className:"border-b border-gray-200 dark:border-gray-700 p-6 pb-4",children:[t.length>1&&i.jsx("nav",{className:"mb-4","aria-label":"Breadcrumb",children:i.jsx("ol",{className:"flex items-center space-x-2 text-sm",children:t.map((s,o)=>i.jsxs("li",{className:"flex items-center",children:[o>0&&i.jsx("span",{className:"text-gray-400 dark:text-gray-500 mx-2",children:"/"}),i.jsx("button",{onClick:()=>n(s.id),className:`
                    hover:text-primary-600 dark:hover:text-primary-400 transition-colors
                    ${s.isActive?"text-gray-900 dark:text-gray-100 font-medium cursor-default":"text-gray-600 dark:text-gray-400"}
                  `,disabled:s.isActive,children:s.title})]},s.id))})}),i.jsx("div",{className:"flex items-start justify-between mb-4",children:i.jsxs("div",{className:"flex-1 min-w-0",children:[i.jsxs("div",{className:"flex items-center space-x-3 mb-2",children:[i.jsxs("span",{className:`
              inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
              ${a(e.level)} bg-gray-100 dark:bg-gray-700
            `,children:["H",e.level]}),i.jsxs("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Section ",e.id.split("-")[0]]})]}),i.jsx("h1",{id:"modal-title",className:"text-2xl font-bold text-gray-900 dark:text-white leading-tight",children:e.title})]})}),i.jsxs("div",{className:"flex items-center space-x-6 text-sm text-gray-600 dark:text-gray-400",children:[i.jsxs("div",{className:"flex items-center space-x-1",children:[i.jsx(B,{className:"h-4 w-4"}),i.jsxs("span",{children:[e.metadata.wordCount," words"]})]}),i.jsxs("div",{className:"flex items-center space-x-1",children:[i.jsx(At,{className:"h-4 w-4"}),i.jsxs("span",{children:[e.metadata.estimatedReadTime," min read"]})]}),e.children.length>0&&i.jsxs("div",{className:"flex items-center space-x-1",children:[i.jsx(Mt,{className:"h-4 w-4"}),i.jsxs("span",{children:[e.children.length," subsections"]})]}),i.jsxs("div",{className:"flex items-center space-x-2",children:[e.metadata.hasCode&&i.jsx("span",{className:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200",children:"Code"}),e.metadata.hasImages&&i.jsx("span",{className:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200",children:"Images"}),e.metadata.hasTables&&i.jsx("span",{className:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200",children:"Tables"}),e.metadata.hasLists&&i.jsx("span",{className:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200",children:"Lists"})]})]})]})},Ys=({node:e})=>{const{openModal:t}=L(),r=e.parent,n=r?.children||[],a=n.findIndex(h=>h.id===e.id),s=a>0?n[a-1]:null,o=a<n.length-1?n[a+1]:null,c=e.children.length>0?e.children[0]:null,u=[{icon:i.jsx(ci,{className:"h-4 w-4"}),label:"Parent",target:r&&r.level>0?r:null,shortcut:"↑",disabled:!r||r.level===0},{icon:i.jsx(Xe,{className:"h-4 w-4"}),label:"Previous",target:s||null,shortcut:"←",disabled:!s},{icon:i.jsx(Qe,{className:"h-4 w-4"}),label:"Next",target:o||null,shortcut:"→",disabled:!o},{icon:i.jsx(ei,{className:"h-4 w-4"}),label:"First Child",target:c||null,shortcut:"↓",disabled:!c}],d=h=>{h&&t(h.id)};return se.useEffect(()=>{const h=l=>{if(l.ctrlKey||l.metaKey)switch(l.key){case"ArrowUp":l.preventDefault(),r&&r.level>0&&d(r);break;case"ArrowLeft":l.preventDefault(),s&&d(s);break;case"ArrowRight":l.preventDefault(),o&&d(o);break;case"ArrowDown":l.preventDefault(),c&&d(c);break}};return document.addEventListener("keydown",h),()=>document.removeEventListener("keydown",h)},[r,s,o,c]),i.jsxs("div",{className:"border-t border-gray-200 dark:border-gray-700 p-4",children:[i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsx("div",{className:"flex items-center space-x-2",children:u.map((h,l)=>i.jsxs("button",{onClick:()=>d(h.target),disabled:h.disabled,className:`
                flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors
                ${h.disabled?"text-gray-400 dark:text-gray-500 cursor-not-allowed":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white"}
              `,title:h.disabled?void 0:`${h.label} (Ctrl+${h.shortcut})`,children:[h.icon,i.jsx("span",{className:"hidden sm:inline",children:h.label}),!h.disabled&&i.jsxs("span",{className:"hidden md:inline text-xs text-gray-400 dark:text-gray-500",children:["Ctrl+",h.shortcut]})]},l))}),i.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:n.length>1&&i.jsxs("span",{children:[a+1," of ",n.length,r&&r.level>0&&i.jsxs("span",{className:"ml-2",children:['in "',r.title,'"']})]})})]}),(s||o)&&i.jsx("div",{className:"mt-4 pt-4 border-t border-gray-100 dark:border-gray-600",children:i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s&&i.jsxs("button",{onClick:()=>d(s),className:"text-left p-3 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",children:[i.jsxs("div",{className:"flex items-center space-x-2 mb-1",children:[i.jsx(Xe,{className:"h-3 w-3 text-gray-400"}),i.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Previous"})]}),i.jsx("div",{className:"font-medium text-gray-900 dark:text-white text-sm truncate",children:s.title})]}),o&&i.jsxs("button",{onClick:()=>d(o),className:"text-left p-3 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",children:[i.jsxs("div",{className:"flex items-center space-x-2 mb-1",children:[i.jsx(Qe,{className:"h-3 w-3 text-gray-400"}),i.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Next"})]}),i.jsx("div",{className:"font-medium text-gray-900 dark:text-white text-sm truncate",children:o.title})]})]})})]})},Js=()=>{const{modal:e,closeModal:t}=L();m.useEffect(()=>{const n=a=>{a.key==="Escape"&&t()};return document.addEventListener("keydown",n),()=>document.removeEventListener("keydown",n)},[t]);const r=m.useCallback(n=>{n.target===n.currentTarget&&t()},[t]);return m.useEffect(()=>(e.isOpen?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[e.isOpen]),!e.isOpen||!e.currentNode?null:i.jsx("div",{className:"modal-overlay animate-fade-in",onClick:r,role:"dialog","aria-modal":"true","aria-labelledby":"modal-title","aria-describedby":"modal-content",children:i.jsxs("div",{className:"modal-content animate-scale-in",children:[i.jsx(Ks,{node:e.currentNode,breadcrumbs:e.breadcrumbs,onClose:t}),i.jsx("div",{className:"flex-1 overflow-hidden flex flex-col",children:i.jsx(Gs,{node:e.currentNode})}),i.jsx(Ys,{node:e.currentNode}),i.jsx("button",{onClick:t,className:"absolute top-4 right-4 p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-500","aria-label":"Close modal",children:i.jsx(J,{className:"h-5 w-5"})})]})})};let Xs=class extends m.Component{constructor(t){super(t),this.state={hasError:!1}}static getDerivedStateFromError(t){return{hasError:!0,error:t}}componentDidCatch(t,r){console.error("ErrorBoundary caught an error:",t,r),this.props.onError?.(t,r)}render(){return this.state.hasError?this.props.fallback||i.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:i.jsxs("div",{className:"max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[i.jsxs("div",{className:"flex items-center mb-4",children:[i.jsx("div",{className:"flex-shrink-0",children:i.jsx("svg",{className:"h-8 w-8 text-red-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),i.jsx("div",{className:"ml-3",children:i.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Something went wrong"})})]}),i.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-300 mb-4",children:"An unexpected error occurred. Please try refreshing the page."}),i.jsx("button",{onClick:()=>window.location.reload(),className:"w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",children:"Refresh Page"}),!1]})}):this.props.children}};function Qs(){const{ui:e,document:t,modal:r,setTheme:n}=L();return m.useEffect(()=>{const a=localStorage.getItem("markdown-visualizer-theme"),s=window.matchMedia("(prefers-color-scheme: dark)").matches,o=a||(s?"dark":"light");n(o),document.documentElement.classList.toggle("dark",o==="dark")},[n]),m.useEffect(()=>{document.documentElement.classList.toggle("dark",e.theme==="dark"),localStorage.setItem("markdown-visualizer-theme",e.theme)},[e.theme]),m.useEffect(()=>{const a=s=>{if(s.ctrlKey||s.metaKey)switch(s.key){case"k":s.preventDefault();const o=document.querySelector("[data-search-input]");o&&o.focus();break;case"b":s.preventDefault(),L.getState().toggleSidebar();break;case"f":s.preventDefault(),L.getState().toggleFullscreen();break}s.key==="Escape"&&r.isOpen&&L.getState().closeModal()};return document.addEventListener("keydown",a),()=>document.removeEventListener("keydown",a)},[r.isOpen]),i.jsx(Xs,{children:i.jsxs("div",{className:`min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300 ${e.isFullscreen?"fullscreen":""}`,children:[i.jsx(ps,{}),i.jsxs("div",{className:"flex h-[calc(100vh-4rem)]",children:[i.jsx(Ps,{}),i.jsx(qs,{})]}),r.isOpen&&i.jsx(Js,{}),t.isLoading&&i.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:i.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 flex items-center space-x-3",children:[i.jsx(_t,{size:"medium"}),i.jsx("span",{className:"text-gray-900 dark:text-gray-100",children:"Processing document..."})]})}),i.jsxs("div",{className:"sr-only",children:[i.jsx("h2",{children:"Keyboard Shortcuts"}),i.jsxs("ul",{children:[i.jsx("li",{children:"Ctrl/Cmd + K: Focus search"}),i.jsx("li",{children:"Ctrl/Cmd + B: Toggle sidebar"}),i.jsx("li",{children:"Ctrl/Cmd + F: Toggle fullscreen"}),i.jsx("li",{children:"Escape: Close modal"})]})]})]})})}class eo extends se.Component{constructor(t){super(t),this.state={hasError:!1}}static getDerivedStateFromError(t){return{hasError:!0,error:t}}componentDidCatch(t,r){console.error("Application error:",t,r)}render(){return this.state.hasError?i.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:i.jsxs("div",{className:"max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[i.jsxs("div",{className:"flex items-center mb-4",children:[i.jsx("div",{className:"flex-shrink-0",children:i.jsx("svg",{className:"h-8 w-8 text-red-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),i.jsx("div",{className:"ml-3",children:i.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Application Error"})})]}),i.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-300 mb-4",children:"Something went wrong. Please refresh the page to try again."}),i.jsx("button",{onClick:()=>window.location.reload(),className:"w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition-colors",children:"Refresh Page"}),!1]})}):this.props.children}}be.createRoot(document.getElementById("root")).render(i.jsx(se.StrictMode,{children:i.jsx(eo,{children:i.jsx(Qs,{})})}));
//# sourceMappingURL=index-79a08009.js.map
