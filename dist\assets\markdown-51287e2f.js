import{g as mt}from"./vendor-27d6d7f2.js";function sn(e){if(e)throw e}var Te=Object.prototype.hasOwnProperty,Rn=Object.prototype.toString,cn=Object.defineProperty,fn=Object.getOwnPropertyDescriptor,hn=function(t){return typeof Array.isArray=="function"?Array.isArray(t):Rn.call(t)==="[object Array]"},pn=function(t){if(!t||Rn.call(t)!=="[object Object]")return!1;var n=Te.call(t,"constructor"),r=t.constructor&&t.constructor.prototype&&Te.call(t.constructor.prototype,"isPrototypeOf");if(t.constructor&&!n&&!r)return!1;var i;for(i in t);return typeof i>"u"||Te.call(t,i)},mn=function(t,n){cn&&n.name==="__proto__"?cn(t,n.name,{enumerable:!0,configurable:!0,value:n.newValue,writable:!0}):t[n.name]=n.newValue},dn=function(t,n){if(n==="__proto__")if(Te.call(t,n)){if(fn)return fn(t,n).value}else return;return t[n]},dt=function e(){var t,n,r,i,u,o,l=arguments[0],s=1,a=arguments.length,f=!1;for(typeof l=="boolean"&&(f=l,l=arguments[1]||{},s=2),(l==null||typeof l!="object"&&typeof l!="function")&&(l={});s<a;++s)if(t=arguments[s],t!=null)for(n in t)r=dn(l,n),i=dn(t,n),l!==i&&(f&&i&&(pn(i)||(u=hn(i)))?(u?(u=!1,o=r&&hn(r)?r:[]):o=r&&pn(r)?r:{},mn(l,{name:n,newValue:e(f,o,i)})):typeof i<"u"&&mn(l,{name:n,newValue:i}));return l};const Be=mt(dt);function Au(){}function je(e){if(typeof e!="object"||e===null)return!1;const t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function gt(){const e=[],t={run:n,use:r};return t;function n(...i){let u=-1;const o=i.pop();if(typeof o!="function")throw new TypeError("Expected function as last argument, not "+o);l(null,...i);function l(s,...a){const f=e[++u];let h=-1;if(s){o(s);return}for(;++h<i.length;)(a[h]===null||a[h]===void 0)&&(a[h]=i[h]);i=a,f?xt(f,l)(...a):o(null,...a)}}function r(i){if(typeof i!="function")throw new TypeError("Expected `middelware` to be a function, not "+i);return e.push(i),t}}function xt(e,t){let n;return r;function r(...o){const l=e.length>o.length;let s;l&&o.push(i);try{s=e.apply(this,o)}catch(a){const f=a;if(l&&n)throw f;return i(f)}l||(s&&s.then&&typeof s.then=="function"?s.then(u,i):s instanceof Error?i(s):u(s))}function i(o,...l){n||(n=!0,t(o,...l))}function u(o){i(null,o)}}function we(e){return!e||typeof e!="object"?"":"position"in e||"type"in e?gn(e.position):"start"in e||"end"in e?gn(e):"line"in e||"column"in e?ve(e):""}function ve(e){return xn(e&&e.line)+":"+xn(e&&e.column)}function gn(e){return ve(e&&e.start)+"-"+ve(e&&e.end)}function xn(e){return e&&typeof e=="number"?e:1}class W extends Error{constructor(t,n,r){super(),typeof n=="string"&&(r=n,n=void 0);let i="",u={},o=!1;if(n&&("line"in n&&"column"in n?u={place:n}:"start"in n&&"end"in n?u={place:n}:"type"in n?u={ancestors:[n],place:n.position}:u={...n}),typeof t=="string"?i=t:!u.cause&&t&&(o=!0,i=t.message,u.cause=t),!u.ruleId&&!u.source&&typeof r=="string"){const s=r.indexOf(":");s===-1?u.ruleId=r:(u.source=r.slice(0,s),u.ruleId=r.slice(s+1))}if(!u.place&&u.ancestors&&u.ancestors){const s=u.ancestors[u.ancestors.length-1];s&&(u.place=s.position)}const l=u.place&&"start"in u.place?u.place.start:u.place;this.ancestors=u.ancestors||void 0,this.cause=u.cause||void 0,this.column=l?l.column:void 0,this.fatal=void 0,this.file,this.message=i,this.line=l?l.line:void 0,this.name=we(u.place)||"1:1",this.place=u.place||void 0,this.reason=this.message,this.ruleId=u.ruleId||void 0,this.source=u.source||void 0,this.stack=o&&u.cause&&typeof u.cause.stack=="string"?u.cause.stack:"",this.actual,this.expected,this.note,this.url}}W.prototype.file="";W.prototype.name="";W.prototype.reason="";W.prototype.message="";W.prototype.stack="";W.prototype.column=void 0;W.prototype.line=void 0;W.prototype.ancestors=void 0;W.prototype.cause=void 0;W.prototype.fatal=void 0;W.prototype.place=void 0;W.prototype.ruleId=void 0;W.prototype.source=void 0;const ne={basename:kt,dirname:yt,extname:bt,join:wt,sep:"/"};function kt(e,t){if(t!==void 0&&typeof t!="string")throw new TypeError('"ext" argument must be a string');Ee(e);let n=0,r=-1,i=e.length,u;if(t===void 0||t.length===0||t.length>e.length){for(;i--;)if(e.codePointAt(i)===47){if(u){n=i+1;break}}else r<0&&(u=!0,r=i+1);return r<0?"":e.slice(n,r)}if(t===e)return"";let o=-1,l=t.length-1;for(;i--;)if(e.codePointAt(i)===47){if(u){n=i+1;break}}else o<0&&(u=!0,o=i+1),l>-1&&(e.codePointAt(i)===t.codePointAt(l--)?l<0&&(r=i):(l=-1,r=o));return n===r?r=o:r<0&&(r=e.length),e.slice(n,r)}function yt(e){if(Ee(e),e.length===0)return".";let t=-1,n=e.length,r;for(;--n;)if(e.codePointAt(n)===47){if(r){t=n;break}}else r||(r=!0);return t<0?e.codePointAt(0)===47?"/":".":t===1&&e.codePointAt(0)===47?"//":e.slice(0,t)}function bt(e){Ee(e);let t=e.length,n=-1,r=0,i=-1,u=0,o;for(;t--;){const l=e.codePointAt(t);if(l===47){if(o){r=t+1;break}continue}n<0&&(o=!0,n=t+1),l===46?i<0?i=t:u!==1&&(u=1):i>-1&&(u=-1)}return i<0||n<0||u===0||u===1&&i===n-1&&i===r+1?"":e.slice(i,n)}function wt(...e){let t=-1,n;for(;++t<e.length;)Ee(e[t]),e[t]&&(n=n===void 0?e[t]:n+"/"+e[t]);return n===void 0?".":St(n)}function St(e){Ee(e);const t=e.codePointAt(0)===47;let n=It(e,!t);return n.length===0&&!t&&(n="."),n.length>0&&e.codePointAt(e.length-1)===47&&(n+="/"),t?"/"+n:n}function It(e,t){let n="",r=0,i=-1,u=0,o=-1,l,s;for(;++o<=e.length;){if(o<e.length)l=e.codePointAt(o);else{if(l===47)break;l=47}if(l===47){if(!(i===o-1||u===1))if(i!==o-1&&u===2){if(n.length<2||r!==2||n.codePointAt(n.length-1)!==46||n.codePointAt(n.length-2)!==46){if(n.length>2){if(s=n.lastIndexOf("/"),s!==n.length-1){s<0?(n="",r=0):(n=n.slice(0,s),r=n.length-1-n.lastIndexOf("/")),i=o,u=0;continue}}else if(n.length>0){n="",r=0,i=o,u=0;continue}}t&&(n=n.length>0?n+"/..":"..",r=2)}else n.length>0?n+="/"+e.slice(i+1,o):n=e.slice(i+1,o),r=o-i-1;i=o,u=0}else l===46&&u>-1?u++:u=-1}return n}function Ee(e){if(typeof e!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}const Et={cwd:Ct};function Ct(){return"/"}function Ue(e){return!!(e!==null&&typeof e=="object"&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&e.auth===void 0)}function zt(e){if(typeof e=="string")e=new URL(e);else if(!Ue(e)){const t=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if(e.protocol!=="file:"){const t=new TypeError("The URL must be of scheme file");throw t.code="ERR_INVALID_URL_SCHEME",t}return Tt(e)}function Tt(e){if(e.hostname!==""){const r=new TypeError('File URL host must be "localhost" or empty on darwin');throw r.code="ERR_INVALID_FILE_URL_HOST",r}const t=e.pathname;let n=-1;for(;++n<t.length;)if(t.codePointAt(n)===37&&t.codePointAt(n+1)===50){const r=t.codePointAt(n+2);if(r===70||r===102){const i=new TypeError("File URL path must not include encoded / characters");throw i.code="ERR_INVALID_FILE_URL_PATH",i}}return decodeURIComponent(t)}const Re=["history","path","basename","stem","extname","dirname"];class At{constructor(t){let n;t?Ue(t)?n={path:t}:typeof t=="string"||_t(t)?n={value:t}:n=t:n={},this.cwd="cwd"in n?"":Et.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<Re.length;){const u=Re[r];u in n&&n[u]!==void 0&&n[u]!==null&&(this[u]=u==="history"?[...n[u]]:n[u])}let i;for(i in n)Re.includes(i)||(this[i]=n[i])}get basename(){return typeof this.path=="string"?ne.basename(this.path):void 0}set basename(t){Me(t,"basename"),Oe(t,"basename"),this.path=ne.join(this.dirname||"",t)}get dirname(){return typeof this.path=="string"?ne.dirname(this.path):void 0}set dirname(t){kn(this.basename,"dirname"),this.path=ne.join(t||"",this.basename)}get extname(){return typeof this.path=="string"?ne.extname(this.path):void 0}set extname(t){if(Oe(t,"extname"),kn(this.dirname,"extname"),t){if(t.codePointAt(0)!==46)throw new Error("`extname` must start with `.`");if(t.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=ne.join(this.dirname,this.stem+(t||""))}get path(){return this.history[this.history.length-1]}set path(t){Ue(t)&&(t=zt(t)),Me(t,"path"),this.path!==t&&this.history.push(t)}get stem(){return typeof this.path=="string"?ne.basename(this.path,this.extname):void 0}set stem(t){Me(t,"stem"),Oe(t,"stem"),this.path=ne.join(this.dirname||"",t+(this.extname||""))}fail(t,n,r){const i=this.message(t,n,r);throw i.fatal=!0,i}info(t,n,r){const i=this.message(t,n,r);return i.fatal=void 0,i}message(t,n,r){const i=new W(t,n,r);return this.path&&(i.name=this.path+":"+i.name,i.file=this.path),i.fatal=!1,this.messages.push(i),i}toString(t){return this.value===void 0?"":typeof this.value=="string"?this.value:new TextDecoder(t||void 0).decode(this.value)}}function Oe(e,t){if(e&&e.includes(ne.sep))throw new Error("`"+t+"` cannot be a path: did not expect `"+ne.sep+"`")}function Me(e,t){if(!e)throw new Error("`"+t+"` cannot be empty")}function kn(e,t){if(!e)throw new Error("Setting `"+t+"` requires `path` to be set too")}function _t(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}const Ft=function(e){const r=this.constructor.prototype,i=r[e],u=function(){return i.apply(u,arguments)};return Object.setPrototypeOf(u,r),u},Pt={}.hasOwnProperty;class Ke extends Ft{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=gt()}copy(){const t=new Ke;let n=-1;for(;++n<this.attachers.length;){const r=this.attachers[n];t.use(...r)}return t.data(Be(!0,{},this.namespace)),t}data(t,n){return typeof t=="string"?arguments.length===2?(qe("data",this.frozen),this.namespace[t]=n,this):Pt.call(this.namespace,t)&&this.namespace[t]||void 0:t?(qe("data",this.frozen),this.namespace=t,this):this.namespace}freeze(){if(this.frozen)return this;const t=this;for(;++this.freezeIndex<this.attachers.length;){const[n,...r]=this.attachers[this.freezeIndex];if(r[0]===!1)continue;r[0]===!0&&(r[0]=void 0);const i=n.call(t,...r);typeof i=="function"&&this.transformers.use(i)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(t){this.freeze();const n=ze(t),r=this.parser||this.Parser;return Ne("parse",r),r(String(n),n)}process(t,n){const r=this;return this.freeze(),Ne("process",this.parser||this.Parser),De("process",this.compiler||this.Compiler),n?i(void 0,n):new Promise(i);function i(u,o){const l=ze(t),s=r.parse(l);r.run(s,l,function(f,h,d){if(f||!h||!d)return a(f);const c=h,E=r.stringify(c,d);Bt(E)?d.value=E:d.result=E,a(f,d)});function a(f,h){f||!h?o(f):u?u(h):n(void 0,h)}}}processSync(t){let n=!1,r;return this.freeze(),Ne("processSync",this.parser||this.Parser),De("processSync",this.compiler||this.Compiler),this.process(t,i),bn("processSync","process",n),r;function i(u,o){n=!0,sn(u),r=o}}run(t,n,r){yn(t),this.freeze();const i=this.transformers;return!r&&typeof n=="function"&&(r=n,n=void 0),r?u(void 0,r):new Promise(u);function u(o,l){const s=ze(n);i.run(t,s,a);function a(f,h,d){const c=h||t;f?l(f):o?o(c):r(void 0,c,d)}}}runSync(t,n){let r=!1,i;return this.run(t,n,u),bn("runSync","run",r),i;function u(o,l){sn(o),i=l,r=!0}}stringify(t,n){this.freeze();const r=ze(n),i=this.compiler||this.Compiler;return De("stringify",i),yn(t),i(t,r)}use(t,...n){const r=this.attachers,i=this.namespace;if(qe("use",this.frozen),t!=null)if(typeof t=="function")s(t,n);else if(typeof t=="object")Array.isArray(t)?l(t):o(t);else throw new TypeError("Expected usable value, not `"+t+"`");return this;function u(a){if(typeof a=="function")s(a,[]);else if(typeof a=="object")if(Array.isArray(a)){const[f,...h]=a;s(f,h)}else o(a);else throw new TypeError("Expected usable value, not `"+a+"`")}function o(a){if(!("plugins"in a)&&!("settings"in a))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");l(a.plugins),a.settings&&(i.settings=Be(!0,i.settings,a.settings))}function l(a){let f=-1;if(a!=null)if(Array.isArray(a))for(;++f<a.length;){const h=a[f];u(h)}else throw new TypeError("Expected a list of plugins, not `"+a+"`")}function s(a,f){let h=-1,d=-1;for(;++h<r.length;)if(r[h][0]===a){d=h;break}if(d===-1)r.push([a,...f]);else if(f.length>0){let[c,...E]=f;const z=r[d][1];je(z)&&je(c)&&(c=Be(!0,z,c)),r[d]=[a,c,...E]}}}}const _u=new Ke().freeze();function Ne(e,t){if(typeof t!="function")throw new TypeError("Cannot `"+e+"` without `parser`")}function De(e,t){if(typeof t!="function")throw new TypeError("Cannot `"+e+"` without `compiler`")}function qe(e,t){if(t)throw new Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function yn(e){if(!je(e)||typeof e.type!="string")throw new TypeError("Expected node, got `"+e+"`")}function bn(e,t,n){if(!n)throw new Error("`"+e+"` finished async. Use `"+t+"` instead")}function ze(e){return Lt(e)?e:new At(e)}function Lt(e){return!!(e&&typeof e=="object"&&"message"in e&&"messages"in e)}function Bt(e){return typeof e=="string"||Rt(e)}function Rt(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}const Ot={};function Ze(e,t){const n=t||Ot,r=typeof n.includeImageAlt=="boolean"?n.includeImageAlt:!0,i=typeof n.includeHtml=="boolean"?n.includeHtml:!0;return On(e,r,i)}function On(e,t,n){if(Mt(e)){if("value"in e)return e.type==="html"&&!n?"":e.value;if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return wn(e.children,t,n)}return Array.isArray(e)?wn(e,t,n):""}function wn(e,t,n){const r=[];let i=-1;for(;++i<e.length;)r[i]=On(e[i],t,n);return r.join("")}function Mt(e){return!!(e&&typeof e=="object")}const Sn=document.createElement("i");function Ge(e){const t="&"+e+";";Sn.innerHTML=t;const n=Sn.textContent;return n.charCodeAt(n.length-1)===59&&e!=="semi"||n===t?!1:n}function re(e,t,n,r){const i=e.length;let u=0,o;if(t<0?t=-t>i?0:i+t:t=t>i?i:t,n=n>0?n:0,r.length<1e4)o=Array.from(r),o.unshift(t,n),e.splice(...o);else for(n&&e.splice(t,n);u<r.length;)o=r.slice(u,u+1e4),o.unshift(t,0),e.splice(...o),u+=1e4,t+=1e4}function K(e,t){return e.length>0?(re(e,e.length,0,t),e):t}const In={}.hasOwnProperty;function Nt(e){const t={};let n=-1;for(;++n<e.length;)Dt(t,e[n]);return t}function Dt(e,t){let n;for(n in t){const i=(In.call(e,n)?e[n]:void 0)||(e[n]={}),u=t[n];let o;if(u)for(o in u){In.call(i,o)||(i[o]=[]);const l=u[o];qt(i[o],Array.isArray(l)?l:l?[l]:[])}}}function qt(e,t){let n=-1;const r=[];for(;++n<t.length;)(t[n].add==="after"?e:r).push(t[n]);re(e,0,0,r)}function Mn(e,t){const n=Number.parseInt(e,t);return n<9||n===11||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(n&65535)===65535||(n&65535)===65534||n>1114111?"�":String.fromCodePoint(n)}function ge(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}const te=ce(/[A-Za-z]/),ee=ce(/[\dA-Za-z]/),Vt=ce(/[#-'*+\--9=?A-Z^-~]/);function Qe(e){return e!==null&&(e<32||e===127)}const $e=ce(/\d/),Ht=ce(/[\dA-Fa-f]/),jt=ce(/[!-/:-@[-`{-~]/);function I(e){return e!==null&&e<-2}function $(e){return e!==null&&(e<0||e===32)}function _(e){return e===-2||e===-1||e===32}const vt=ce(/\p{P}|\p{S}/u),Ut=ce(/\s/);function ce(e){return t;function t(n){return n!==null&&n>-1&&e.test(String.fromCharCode(n))}}function B(e,t,n,r){const i=r?r-1:Number.POSITIVE_INFINITY;let u=0;return o;function o(s){return _(s)?(e.enter(n),l(s)):t(s)}function l(s){return _(s)&&u++<i?(e.consume(s),l):(e.exit(n),t(s))}}const Qt={tokenize:$t};function $t(e){const t=e.attempt(this.parser.constructs.contentInitial,r,i);let n;return t;function r(l){if(l===null){e.consume(l);return}return e.enter("lineEnding"),e.consume(l),e.exit("lineEnding"),B(e,t,"linePrefix")}function i(l){return e.enter("paragraph"),u(l)}function u(l){const s=e.enter("chunkText",{contentType:"text",previous:n});return n&&(n.next=s),n=s,o(l)}function o(l){if(l===null){e.exit("chunkText"),e.exit("paragraph"),e.consume(l);return}return I(l)?(e.consume(l),e.exit("chunkText"),u):(e.consume(l),o)}}const Wt={tokenize:Yt},En={tokenize:Kt};function Yt(e){const t=this,n=[];let r=0,i,u,o;return l;function l(b){if(r<n.length){const N=n[r];return t.containerState=N[1],e.attempt(N[0].continuation,s,a)(b)}return a(b)}function s(b){if(r++,t.containerState._closeFlow){t.containerState._closeFlow=void 0,i&&M();const N=t.events.length;let q=N,x;for(;q--;)if(t.events[q][0]==="exit"&&t.events[q][1].type==="chunkFlow"){x=t.events[q][1].end;break}y(r);let F=N;for(;F<t.events.length;)t.events[F][1].end={...x},F++;return re(t.events,q+1,0,t.events.slice(N)),t.events.length=F,a(b)}return l(b)}function a(b){if(r===n.length){if(!i)return d(b);if(i.currentConstruct&&i.currentConstruct.concrete)return E(b);t.interrupt=!!(i.currentConstruct&&!i._gfmTableDynamicInterruptHack)}return t.containerState={},e.check(En,f,h)(b)}function f(b){return i&&M(),y(r),d(b)}function h(b){return t.parser.lazy[t.now().line]=r!==n.length,o=t.now().offset,E(b)}function d(b){return t.containerState={},e.attempt(En,c,E)(b)}function c(b){return r++,n.push([t.currentConstruct,t.containerState]),d(b)}function E(b){if(b===null){i&&M(),y(0),e.consume(b);return}return i=i||t.parser.flow(t.now()),e.enter("chunkFlow",{_tokenizer:i,contentType:"flow",previous:u}),z(b)}function z(b){if(b===null){R(e.exit("chunkFlow"),!0),y(0),e.consume(b);return}return I(b)?(e.consume(b),R(e.exit("chunkFlow")),r=0,t.interrupt=void 0,l):(e.consume(b),z)}function R(b,N){const q=t.sliceStream(b);if(N&&q.push(null),b.previous=u,u&&(u.next=b),u=b,i.defineSkip(b.start),i.write(q),t.parser.lazy[b.start.line]){let x=i.events.length;for(;x--;)if(i.events[x][1].start.offset<o&&(!i.events[x][1].end||i.events[x][1].end.offset>o))return;const F=t.events.length;let V=F,P,O;for(;V--;)if(t.events[V][0]==="exit"&&t.events[V][1].type==="chunkFlow"){if(P){O=t.events[V][1].end;break}P=!0}for(y(r),x=F;x<t.events.length;)t.events[x][1].end={...O},x++;re(t.events,V+1,0,t.events.slice(F)),t.events.length=x}}function y(b){let N=n.length;for(;N-- >b;){const q=n[N];t.containerState=q[1],q[0].exit.call(t,e)}n.length=b}function M(){i.write([null]),u=void 0,i=void 0,t.containerState._closeFlow=void 0}}function Kt(e,t,n){return B(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function _e(e){if(e===null||$(e)||Ut(e))return 1;if(vt(e))return 2}function Je(e,t,n){const r=[];let i=-1;for(;++i<e.length;){const u=e[i].resolveAll;u&&!r.includes(u)&&(t=u(t,n),r.push(u))}return t}const We={name:"attention",resolveAll:Zt,tokenize:Gt};function Zt(e,t){let n=-1,r,i,u,o,l,s,a,f;for(;++n<e.length;)if(e[n][0]==="enter"&&e[n][1].type==="attentionSequence"&&e[n][1]._close){for(r=n;r--;)if(e[r][0]==="exit"&&e[r][1].type==="attentionSequence"&&e[r][1]._open&&t.sliceSerialize(e[r][1]).charCodeAt(0)===t.sliceSerialize(e[n][1]).charCodeAt(0)){if((e[r][1]._close||e[n][1]._open)&&(e[n][1].end.offset-e[n][1].start.offset)%3&&!((e[r][1].end.offset-e[r][1].start.offset+e[n][1].end.offset-e[n][1].start.offset)%3))continue;s=e[r][1].end.offset-e[r][1].start.offset>1&&e[n][1].end.offset-e[n][1].start.offset>1?2:1;const h={...e[r][1].end},d={...e[n][1].start};Cn(h,-s),Cn(d,s),o={type:s>1?"strongSequence":"emphasisSequence",start:h,end:{...e[r][1].end}},l={type:s>1?"strongSequence":"emphasisSequence",start:{...e[n][1].start},end:d},u={type:s>1?"strongText":"emphasisText",start:{...e[r][1].end},end:{...e[n][1].start}},i={type:s>1?"strong":"emphasis",start:{...o.start},end:{...l.end}},e[r][1].end={...o.start},e[n][1].start={...l.end},a=[],e[r][1].end.offset-e[r][1].start.offset&&(a=K(a,[["enter",e[r][1],t],["exit",e[r][1],t]])),a=K(a,[["enter",i,t],["enter",o,t],["exit",o,t],["enter",u,t]]),a=K(a,Je(t.parser.constructs.insideSpan.null,e.slice(r+1,n),t)),a=K(a,[["exit",u,t],["enter",l,t],["exit",l,t],["exit",i,t]]),e[n][1].end.offset-e[n][1].start.offset?(f=2,a=K(a,[["enter",e[n][1],t],["exit",e[n][1],t]])):f=0,re(e,r-1,n-r+3,a),n=r+a.length-f-2;break}}for(n=-1;++n<e.length;)e[n][1].type==="attentionSequence"&&(e[n][1].type="data");return e}function Gt(e,t){const n=this.parser.constructs.attentionMarkers.null,r=this.previous,i=_e(r);let u;return o;function o(s){return u=s,e.enter("attentionSequence"),l(s)}function l(s){if(s===u)return e.consume(s),l;const a=e.exit("attentionSequence"),f=_e(s),h=!f||f===2&&i||n.includes(s),d=!i||i===2&&f||n.includes(r);return a._open=!!(u===42?h:h&&(i||!d)),a._close=!!(u===42?d:d&&(f||!h)),t(s)}}function Cn(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}const Jt={name:"autolink",tokenize:Xt};function Xt(e,t,n){let r=0;return i;function i(c){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(c),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),u}function u(c){return te(c)?(e.consume(c),o):c===64?n(c):a(c)}function o(c){return c===43||c===45||c===46||ee(c)?(r=1,l(c)):a(c)}function l(c){return c===58?(e.consume(c),r=0,s):(c===43||c===45||c===46||ee(c))&&r++<32?(e.consume(c),l):(r=0,a(c))}function s(c){return c===62?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(c),e.exit("autolinkMarker"),e.exit("autolink"),t):c===null||c===32||c===60||Qe(c)?n(c):(e.consume(c),s)}function a(c){return c===64?(e.consume(c),f):Vt(c)?(e.consume(c),a):n(c)}function f(c){return ee(c)?h(c):n(c)}function h(c){return c===46?(e.consume(c),r=0,f):c===62?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(c),e.exit("autolinkMarker"),e.exit("autolink"),t):d(c)}function d(c){if((c===45||ee(c))&&r++<63){const E=c===45?d:h;return e.consume(c),E}return n(c)}}const Pe={partial:!0,tokenize:er};function er(e,t,n){return r;function r(u){return _(u)?B(e,i,"linePrefix")(u):i(u)}function i(u){return u===null||I(u)?t(u):n(u)}}const Nn={continuation:{tokenize:tr},exit:rr,name:"blockQuote",tokenize:nr};function nr(e,t,n){const r=this;return i;function i(o){if(o===62){const l=r.containerState;return l.open||(e.enter("blockQuote",{_container:!0}),l.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(o),e.exit("blockQuoteMarker"),u}return n(o)}function u(o){return _(o)?(e.enter("blockQuotePrefixWhitespace"),e.consume(o),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(o))}}function tr(e,t,n){const r=this;return i;function i(o){return _(o)?B(e,u,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(o):u(o)}function u(o){return e.attempt(Nn,t,n)(o)}}function rr(e){e.exit("blockQuote")}const Dn={name:"characterEscape",tokenize:ir};function ir(e,t,n){return r;function r(u){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(u),e.exit("escapeMarker"),i}function i(u){return jt(u)?(e.enter("characterEscapeValue"),e.consume(u),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(u)}}const qn={name:"characterReference",tokenize:ur};function ur(e,t,n){const r=this;let i=0,u,o;return l;function l(h){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(h),e.exit("characterReferenceMarker"),s}function s(h){return h===35?(e.enter("characterReferenceMarkerNumeric"),e.consume(h),e.exit("characterReferenceMarkerNumeric"),a):(e.enter("characterReferenceValue"),u=31,o=ee,f(h))}function a(h){return h===88||h===120?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(h),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),u=6,o=Ht,f):(e.enter("characterReferenceValue"),u=7,o=$e,f(h))}function f(h){if(h===59&&i){const d=e.exit("characterReferenceValue");return o===ee&&!Ge(r.sliceSerialize(d))?n(h):(e.enter("characterReferenceMarker"),e.consume(h),e.exit("characterReferenceMarker"),e.exit("characterReference"),t)}return o(h)&&i++<u?(e.consume(h),f):n(h)}}const zn={partial:!0,tokenize:lr},Tn={concrete:!0,name:"codeFenced",tokenize:or};function or(e,t,n){const r=this,i={partial:!0,tokenize:q};let u=0,o=0,l;return s;function s(x){return a(x)}function a(x){const F=r.events[r.events.length-1];return u=F&&F[1].type==="linePrefix"?F[2].sliceSerialize(F[1],!0).length:0,l=x,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),f(x)}function f(x){return x===l?(o++,e.consume(x),f):o<3?n(x):(e.exit("codeFencedFenceSequence"),_(x)?B(e,h,"whitespace")(x):h(x))}function h(x){return x===null||I(x)?(e.exit("codeFencedFence"),r.interrupt?t(x):e.check(zn,z,N)(x)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),d(x))}function d(x){return x===null||I(x)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),h(x)):_(x)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),B(e,c,"whitespace")(x)):x===96&&x===l?n(x):(e.consume(x),d)}function c(x){return x===null||I(x)?h(x):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),E(x))}function E(x){return x===null||I(x)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),h(x)):x===96&&x===l?n(x):(e.consume(x),E)}function z(x){return e.attempt(i,N,R)(x)}function R(x){return e.enter("lineEnding"),e.consume(x),e.exit("lineEnding"),y}function y(x){return u>0&&_(x)?B(e,M,"linePrefix",u+1)(x):M(x)}function M(x){return x===null||I(x)?e.check(zn,z,N)(x):(e.enter("codeFlowValue"),b(x))}function b(x){return x===null||I(x)?(e.exit("codeFlowValue"),M(x)):(e.consume(x),b)}function N(x){return e.exit("codeFenced"),t(x)}function q(x,F,V){let P=0;return O;function O(A){return x.enter("lineEnding"),x.consume(A),x.exit("lineEnding"),S}function S(A){return x.enter("codeFencedFence"),_(A)?B(x,w,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(A):w(A)}function w(A){return A===l?(x.enter("codeFencedFenceSequence"),D(A)):V(A)}function D(A){return A===l?(P++,x.consume(A),D):P>=o?(x.exit("codeFencedFenceSequence"),_(A)?B(x,H,"whitespace")(A):H(A)):V(A)}function H(A){return A===null||I(A)?(x.exit("codeFencedFence"),F(A)):V(A)}}}function lr(e,t,n){const r=this;return i;function i(o){return o===null?n(o):(e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),u)}function u(o){return r.parser.lazy[r.now().line]?n(o):t(o)}}const Ve={name:"codeIndented",tokenize:sr},ar={partial:!0,tokenize:cr};function sr(e,t,n){const r=this;return i;function i(a){return e.enter("codeIndented"),B(e,u,"linePrefix",4+1)(a)}function u(a){const f=r.events[r.events.length-1];return f&&f[1].type==="linePrefix"&&f[2].sliceSerialize(f[1],!0).length>=4?o(a):n(a)}function o(a){return a===null?s(a):I(a)?e.attempt(ar,o,s)(a):(e.enter("codeFlowValue"),l(a))}function l(a){return a===null||I(a)?(e.exit("codeFlowValue"),o(a)):(e.consume(a),l)}function s(a){return e.exit("codeIndented"),t(a)}}function cr(e,t,n){const r=this;return i;function i(o){return r.parser.lazy[r.now().line]?n(o):I(o)?(e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),i):B(e,u,"linePrefix",4+1)(o)}function u(o){const l=r.events[r.events.length-1];return l&&l[1].type==="linePrefix"&&l[2].sliceSerialize(l[1],!0).length>=4?t(o):I(o)?i(o):n(o)}}const fr={name:"codeText",previous:pr,resolve:hr,tokenize:mr};function hr(e){let t=e.length-4,n=3,r,i;if((e[n][1].type==="lineEnding"||e[n][1].type==="space")&&(e[t][1].type==="lineEnding"||e[t][1].type==="space")){for(r=n;++r<t;)if(e[r][1].type==="codeTextData"){e[n][1].type="codeTextPadding",e[t][1].type="codeTextPadding",n+=2,t-=2;break}}for(r=n-1,t++;++r<=t;)i===void 0?r!==t&&e[r][1].type!=="lineEnding"&&(i=r):(r===t||e[r][1].type==="lineEnding")&&(e[i][1].type="codeTextData",r!==i+2&&(e[i][1].end=e[r-1][1].end,e.splice(i+2,r-i-2),t-=r-i-2,r=i+2),i=void 0);return e}function pr(e){return e!==96||this.events[this.events.length-1][1].type==="characterEscape"}function mr(e,t,n){let r=0,i,u;return o;function o(h){return e.enter("codeText"),e.enter("codeTextSequence"),l(h)}function l(h){return h===96?(e.consume(h),r++,l):(e.exit("codeTextSequence"),s(h))}function s(h){return h===null?n(h):h===32?(e.enter("space"),e.consume(h),e.exit("space"),s):h===96?(u=e.enter("codeTextSequence"),i=0,f(h)):I(h)?(e.enter("lineEnding"),e.consume(h),e.exit("lineEnding"),s):(e.enter("codeTextData"),a(h))}function a(h){return h===null||h===32||h===96||I(h)?(e.exit("codeTextData"),s(h)):(e.consume(h),a)}function f(h){return h===96?(e.consume(h),i++,f):i===r?(e.exit("codeTextSequence"),e.exit("codeText"),t(h)):(u.type="codeTextData",a(h))}}class dr{constructor(t){this.left=t?[...t]:[],this.right=[]}get(t){if(t<0||t>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+t+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return t<this.left.length?this.left[t]:this.right[this.right.length-t+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(t,n){const r=n??Number.POSITIVE_INFINITY;return r<this.left.length?this.left.slice(t,r):t>this.left.length?this.right.slice(this.right.length-r+this.left.length,this.right.length-t+this.left.length).reverse():this.left.slice(t).concat(this.right.slice(this.right.length-r+this.left.length).reverse())}splice(t,n,r){const i=n||0;this.setCursor(Math.trunc(t));const u=this.right.splice(this.right.length-i,Number.POSITIVE_INFINITY);return r&&be(this.left,r),u.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(t){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(t)}pushMany(t){this.setCursor(Number.POSITIVE_INFINITY),be(this.left,t)}unshift(t){this.setCursor(0),this.right.push(t)}unshiftMany(t){this.setCursor(0),be(this.right,t.reverse())}setCursor(t){if(!(t===this.left.length||t>this.left.length&&this.right.length===0||t<0&&this.left.length===0))if(t<this.left.length){const n=this.left.splice(t,Number.POSITIVE_INFINITY);be(this.right,n.reverse())}else{const n=this.right.splice(this.left.length+this.right.length-t,Number.POSITIVE_INFINITY);be(this.left,n.reverse())}}}function be(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function Vn(e){const t={};let n=-1,r,i,u,o,l,s,a;const f=new dr(e);for(;++n<f.length;){for(;n in t;)n=t[n];if(r=f.get(n),n&&r[1].type==="chunkFlow"&&f.get(n-1)[1].type==="listItemPrefix"&&(s=r[1]._tokenizer.events,u=0,u<s.length&&s[u][1].type==="lineEndingBlank"&&(u+=2),u<s.length&&s[u][1].type==="content"))for(;++u<s.length&&s[u][1].type!=="content";)s[u][1].type==="chunkText"&&(s[u][1]._isInFirstContentOfListItem=!0,u++);if(r[0]==="enter")r[1].contentType&&(Object.assign(t,gr(f,n)),n=t[n],a=!0);else if(r[1]._container){for(u=n,i=void 0;u--;)if(o=f.get(u),o[1].type==="lineEnding"||o[1].type==="lineEndingBlank")o[0]==="enter"&&(i&&(f.get(i)[1].type="lineEndingBlank"),o[1].type="lineEnding",i=u);else if(!(o[1].type==="linePrefix"||o[1].type==="listItemIndent"))break;i&&(r[1].end={...f.get(i)[1].start},l=f.slice(i,n),l.unshift(r),f.splice(i,n-i+1,l))}}return re(e,0,Number.POSITIVE_INFINITY,f.slice(0)),!a}function gr(e,t){const n=e.get(t)[1],r=e.get(t)[2];let i=t-1;const u=[];let o=n._tokenizer;o||(o=r.parser[n.contentType](n.start),n._contentTypeTextTrailing&&(o._contentTypeTextTrailing=!0));const l=o.events,s=[],a={};let f,h,d=-1,c=n,E=0,z=0;const R=[z];for(;c;){for(;e.get(++i)[1]!==c;);u.push(i),c._tokenizer||(f=r.sliceStream(c),c.next||f.push(null),h&&o.defineSkip(c.start),c._isInFirstContentOfListItem&&(o._gfmTasklistFirstContentOfListItem=!0),o.write(f),c._isInFirstContentOfListItem&&(o._gfmTasklistFirstContentOfListItem=void 0)),h=c,c=c.next}for(c=n;++d<l.length;)l[d][0]==="exit"&&l[d-1][0]==="enter"&&l[d][1].type===l[d-1][1].type&&l[d][1].start.line!==l[d][1].end.line&&(z=d+1,R.push(z),c._tokenizer=void 0,c.previous=void 0,c=c.next);for(o.events=[],c?(c._tokenizer=void 0,c.previous=void 0):R.pop(),d=R.length;d--;){const y=l.slice(R[d],R[d+1]),M=u.pop();s.push([M,M+y.length-1]),e.splice(M,2,y)}for(s.reverse(),d=-1;++d<s.length;)a[E+s[d][0]]=E+s[d][1],E+=s[d][1]-s[d][0]-1;return a}const xr={resolve:yr,tokenize:br},kr={partial:!0,tokenize:wr};function yr(e){return Vn(e),e}function br(e,t){let n;return r;function r(l){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),i(l)}function i(l){return l===null?u(l):I(l)?e.check(kr,o,u)(l):(e.consume(l),i)}function u(l){return e.exit("chunkContent"),e.exit("content"),t(l)}function o(l){return e.consume(l),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,i}}function wr(e,t,n){const r=this;return i;function i(o){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),B(e,u,"linePrefix")}function u(o){if(o===null||I(o))return n(o);const l=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&l&&l[1].type==="linePrefix"&&l[2].sliceSerialize(l[1],!0).length>=4?t(o):e.interrupt(r.parser.constructs.flow,n,t)(o)}}function Hn(e,t,n,r,i,u,o,l,s){const a=s||Number.POSITIVE_INFINITY;let f=0;return h;function h(y){return y===60?(e.enter(r),e.enter(i),e.enter(u),e.consume(y),e.exit(u),d):y===null||y===32||y===41||Qe(y)?n(y):(e.enter(r),e.enter(o),e.enter(l),e.enter("chunkString",{contentType:"string"}),z(y))}function d(y){return y===62?(e.enter(u),e.consume(y),e.exit(u),e.exit(i),e.exit(r),t):(e.enter(l),e.enter("chunkString",{contentType:"string"}),c(y))}function c(y){return y===62?(e.exit("chunkString"),e.exit(l),d(y)):y===null||y===60||I(y)?n(y):(e.consume(y),y===92?E:c)}function E(y){return y===60||y===62||y===92?(e.consume(y),c):c(y)}function z(y){return!f&&(y===null||y===41||$(y))?(e.exit("chunkString"),e.exit(l),e.exit(o),e.exit(r),t(y)):f<a&&y===40?(e.consume(y),f++,z):y===41?(e.consume(y),f--,z):y===null||y===32||y===40||Qe(y)?n(y):(e.consume(y),y===92?R:z)}function R(y){return y===40||y===41||y===92?(e.consume(y),z):z(y)}}function jn(e,t,n,r,i,u){const o=this;let l=0,s;return a;function a(c){return e.enter(r),e.enter(i),e.consume(c),e.exit(i),e.enter(u),f}function f(c){return l>999||c===null||c===91||c===93&&!s||c===94&&!l&&"_hiddenFootnoteSupport"in o.parser.constructs?n(c):c===93?(e.exit(u),e.enter(i),e.consume(c),e.exit(i),e.exit(r),t):I(c)?(e.enter("lineEnding"),e.consume(c),e.exit("lineEnding"),f):(e.enter("chunkString",{contentType:"string"}),h(c))}function h(c){return c===null||c===91||c===93||I(c)||l++>999?(e.exit("chunkString"),f(c)):(e.consume(c),s||(s=!_(c)),c===92?d:h)}function d(c){return c===91||c===92||c===93?(e.consume(c),l++,h):h(c)}}function vn(e,t,n,r,i,u){let o;return l;function l(d){return d===34||d===39||d===40?(e.enter(r),e.enter(i),e.consume(d),e.exit(i),o=d===40?41:d,s):n(d)}function s(d){return d===o?(e.enter(i),e.consume(d),e.exit(i),e.exit(r),t):(e.enter(u),a(d))}function a(d){return d===o?(e.exit(u),s(o)):d===null?n(d):I(d)?(e.enter("lineEnding"),e.consume(d),e.exit("lineEnding"),B(e,a,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),f(d))}function f(d){return d===o||d===null||I(d)?(e.exit("chunkString"),a(d)):(e.consume(d),d===92?h:f)}function h(d){return d===o||d===92?(e.consume(d),f):f(d)}}function Se(e,t){let n;return r;function r(i){return I(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),n=!0,r):_(i)?B(e,r,n?"linePrefix":"lineSuffix")(i):t(i)}}const Sr={name:"definition",tokenize:Er},Ir={partial:!0,tokenize:Cr};function Er(e,t,n){const r=this;let i;return u;function u(c){return e.enter("definition"),o(c)}function o(c){return jn.call(r,e,l,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(c)}function l(c){return i=ge(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)),c===58?(e.enter("definitionMarker"),e.consume(c),e.exit("definitionMarker"),s):n(c)}function s(c){return $(c)?Se(e,a)(c):a(c)}function a(c){return Hn(e,f,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(c)}function f(c){return e.attempt(Ir,h,h)(c)}function h(c){return _(c)?B(e,d,"whitespace")(c):d(c)}function d(c){return c===null||I(c)?(e.exit("definition"),r.parser.defined.push(i),t(c)):n(c)}}function Cr(e,t,n){return r;function r(l){return $(l)?Se(e,i)(l):n(l)}function i(l){return vn(e,u,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(l)}function u(l){return _(l)?B(e,o,"whitespace")(l):o(l)}function o(l){return l===null||I(l)?t(l):n(l)}}const zr={name:"hardBreakEscape",tokenize:Tr};function Tr(e,t,n){return r;function r(u){return e.enter("hardBreakEscape"),e.consume(u),i}function i(u){return I(u)?(e.exit("hardBreakEscape"),t(u)):n(u)}}const Ar={name:"headingAtx",resolve:_r,tokenize:Fr};function _r(e,t){let n=e.length-2,r=3,i,u;return e[r][1].type==="whitespace"&&(r+=2),n-2>r&&e[n][1].type==="whitespace"&&(n-=2),e[n][1].type==="atxHeadingSequence"&&(r===n-1||n-4>r&&e[n-2][1].type==="whitespace")&&(n-=r+1===n?2:4),n>r&&(i={type:"atxHeadingText",start:e[r][1].start,end:e[n][1].end},u={type:"chunkText",start:e[r][1].start,end:e[n][1].end,contentType:"text"},re(e,r,n-r+1,[["enter",i,t],["enter",u,t],["exit",u,t],["exit",i,t]])),e}function Fr(e,t,n){let r=0;return i;function i(f){return e.enter("atxHeading"),u(f)}function u(f){return e.enter("atxHeadingSequence"),o(f)}function o(f){return f===35&&r++<6?(e.consume(f),o):f===null||$(f)?(e.exit("atxHeadingSequence"),l(f)):n(f)}function l(f){return f===35?(e.enter("atxHeadingSequence"),s(f)):f===null||I(f)?(e.exit("atxHeading"),t(f)):_(f)?B(e,l,"whitespace")(f):(e.enter("atxHeadingText"),a(f))}function s(f){return f===35?(e.consume(f),s):(e.exit("atxHeadingSequence"),l(f))}function a(f){return f===null||f===35||$(f)?(e.exit("atxHeadingText"),l(f)):(e.consume(f),a)}}const Pr=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],An=["pre","script","style","textarea"],Lr={concrete:!0,name:"htmlFlow",resolveTo:Or,tokenize:Mr},Br={partial:!0,tokenize:Dr},Rr={partial:!0,tokenize:Nr};function Or(e){let t=e.length;for(;t--&&!(e[t][0]==="enter"&&e[t][1].type==="htmlFlow"););return t>1&&e[t-2][1].type==="linePrefix"&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e}function Mr(e,t,n){const r=this;let i,u,o,l,s;return a;function a(m){return f(m)}function f(m){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(m),h}function h(m){return m===33?(e.consume(m),d):m===47?(e.consume(m),u=!0,z):m===63?(e.consume(m),i=3,r.interrupt?t:p):te(m)?(e.consume(m),o=String.fromCharCode(m),R):n(m)}function d(m){return m===45?(e.consume(m),i=2,c):m===91?(e.consume(m),i=5,l=0,E):te(m)?(e.consume(m),i=4,r.interrupt?t:p):n(m)}function c(m){return m===45?(e.consume(m),r.interrupt?t:p):n(m)}function E(m){const J="CDATA[";return m===J.charCodeAt(l++)?(e.consume(m),l===J.length?r.interrupt?t:w:E):n(m)}function z(m){return te(m)?(e.consume(m),o=String.fromCharCode(m),R):n(m)}function R(m){if(m===null||m===47||m===62||$(m)){const J=m===47,fe=o.toLowerCase();return!J&&!u&&An.includes(fe)?(i=1,r.interrupt?t(m):w(m)):Pr.includes(o.toLowerCase())?(i=6,J?(e.consume(m),y):r.interrupt?t(m):w(m)):(i=7,r.interrupt&&!r.parser.lazy[r.now().line]?n(m):u?M(m):b(m))}return m===45||ee(m)?(e.consume(m),o+=String.fromCharCode(m),R):n(m)}function y(m){return m===62?(e.consume(m),r.interrupt?t:w):n(m)}function M(m){return _(m)?(e.consume(m),M):O(m)}function b(m){return m===47?(e.consume(m),O):m===58||m===95||te(m)?(e.consume(m),N):_(m)?(e.consume(m),b):O(m)}function N(m){return m===45||m===46||m===58||m===95||ee(m)?(e.consume(m),N):q(m)}function q(m){return m===61?(e.consume(m),x):_(m)?(e.consume(m),q):b(m)}function x(m){return m===null||m===60||m===61||m===62||m===96?n(m):m===34||m===39?(e.consume(m),s=m,F):_(m)?(e.consume(m),x):V(m)}function F(m){return m===s?(e.consume(m),s=null,P):m===null||I(m)?n(m):(e.consume(m),F)}function V(m){return m===null||m===34||m===39||m===47||m===60||m===61||m===62||m===96||$(m)?q(m):(e.consume(m),V)}function P(m){return m===47||m===62||_(m)?b(m):n(m)}function O(m){return m===62?(e.consume(m),S):n(m)}function S(m){return m===null||I(m)?w(m):_(m)?(e.consume(m),S):n(m)}function w(m){return m===45&&i===2?(e.consume(m),U):m===60&&i===1?(e.consume(m),j):m===62&&i===4?(e.consume(m),G):m===63&&i===3?(e.consume(m),p):m===93&&i===5?(e.consume(m),ie):I(m)&&(i===6||i===7)?(e.exit("htmlFlowData"),e.check(Br,ue,D)(m)):m===null||I(m)?(e.exit("htmlFlowData"),D(m)):(e.consume(m),w)}function D(m){return e.check(Rr,H,ue)(m)}function H(m){return e.enter("lineEnding"),e.consume(m),e.exit("lineEnding"),A}function A(m){return m===null||I(m)?D(m):(e.enter("htmlFlowData"),w(m))}function U(m){return m===45?(e.consume(m),p):w(m)}function j(m){return m===47?(e.consume(m),o="",Z):w(m)}function Z(m){if(m===62){const J=o.toLowerCase();return An.includes(J)?(e.consume(m),G):w(m)}return te(m)&&o.length<8?(e.consume(m),o+=String.fromCharCode(m),Z):w(m)}function ie(m){return m===93?(e.consume(m),p):w(m)}function p(m){return m===62?(e.consume(m),G):m===45&&i===2?(e.consume(m),p):w(m)}function G(m){return m===null||I(m)?(e.exit("htmlFlowData"),ue(m)):(e.consume(m),G)}function ue(m){return e.exit("htmlFlow"),t(m)}}function Nr(e,t,n){const r=this;return i;function i(o){return I(o)?(e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),u):n(o)}function u(o){return r.parser.lazy[r.now().line]?n(o):t(o)}}function Dr(e,t,n){return r;function r(i){return e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),e.attempt(Pe,t,n)}}const qr={name:"htmlText",tokenize:Vr};function Vr(e,t,n){const r=this;let i,u,o;return l;function l(p){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(p),s}function s(p){return p===33?(e.consume(p),a):p===47?(e.consume(p),q):p===63?(e.consume(p),b):te(p)?(e.consume(p),V):n(p)}function a(p){return p===45?(e.consume(p),f):p===91?(e.consume(p),u=0,E):te(p)?(e.consume(p),M):n(p)}function f(p){return p===45?(e.consume(p),c):n(p)}function h(p){return p===null?n(p):p===45?(e.consume(p),d):I(p)?(o=h,j(p)):(e.consume(p),h)}function d(p){return p===45?(e.consume(p),c):h(p)}function c(p){return p===62?U(p):p===45?d(p):h(p)}function E(p){const G="CDATA[";return p===G.charCodeAt(u++)?(e.consume(p),u===G.length?z:E):n(p)}function z(p){return p===null?n(p):p===93?(e.consume(p),R):I(p)?(o=z,j(p)):(e.consume(p),z)}function R(p){return p===93?(e.consume(p),y):z(p)}function y(p){return p===62?U(p):p===93?(e.consume(p),y):z(p)}function M(p){return p===null||p===62?U(p):I(p)?(o=M,j(p)):(e.consume(p),M)}function b(p){return p===null?n(p):p===63?(e.consume(p),N):I(p)?(o=b,j(p)):(e.consume(p),b)}function N(p){return p===62?U(p):b(p)}function q(p){return te(p)?(e.consume(p),x):n(p)}function x(p){return p===45||ee(p)?(e.consume(p),x):F(p)}function F(p){return I(p)?(o=F,j(p)):_(p)?(e.consume(p),F):U(p)}function V(p){return p===45||ee(p)?(e.consume(p),V):p===47||p===62||$(p)?P(p):n(p)}function P(p){return p===47?(e.consume(p),U):p===58||p===95||te(p)?(e.consume(p),O):I(p)?(o=P,j(p)):_(p)?(e.consume(p),P):U(p)}function O(p){return p===45||p===46||p===58||p===95||ee(p)?(e.consume(p),O):S(p)}function S(p){return p===61?(e.consume(p),w):I(p)?(o=S,j(p)):_(p)?(e.consume(p),S):P(p)}function w(p){return p===null||p===60||p===61||p===62||p===96?n(p):p===34||p===39?(e.consume(p),i=p,D):I(p)?(o=w,j(p)):_(p)?(e.consume(p),w):(e.consume(p),H)}function D(p){return p===i?(e.consume(p),i=void 0,A):p===null?n(p):I(p)?(o=D,j(p)):(e.consume(p),D)}function H(p){return p===null||p===34||p===39||p===60||p===61||p===96?n(p):p===47||p===62||$(p)?P(p):(e.consume(p),H)}function A(p){return p===47||p===62||$(p)?P(p):n(p)}function U(p){return p===62?(e.consume(p),e.exit("htmlTextData"),e.exit("htmlText"),t):n(p)}function j(p){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(p),e.exit("lineEnding"),Z}function Z(p){return _(p)?B(e,ie,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(p):ie(p)}function ie(p){return e.enter("htmlTextData"),o(p)}}const Xe={name:"labelEnd",resolveAll:Ur,resolveTo:Qr,tokenize:$r},Hr={tokenize:Wr},jr={tokenize:Yr},vr={tokenize:Kr};function Ur(e){let t=-1;const n=[];for(;++t<e.length;){const r=e[t][1];if(n.push(e[t]),r.type==="labelImage"||r.type==="labelLink"||r.type==="labelEnd"){const i=r.type==="labelImage"?4:2;r.type="data",t+=i}}return e.length!==n.length&&re(e,0,e.length,n),e}function Qr(e,t){let n=e.length,r=0,i,u,o,l;for(;n--;)if(i=e[n][1],u){if(i.type==="link"||i.type==="labelLink"&&i._inactive)break;e[n][0]==="enter"&&i.type==="labelLink"&&(i._inactive=!0)}else if(o){if(e[n][0]==="enter"&&(i.type==="labelImage"||i.type==="labelLink")&&!i._balanced&&(u=n,i.type!=="labelLink")){r=2;break}}else i.type==="labelEnd"&&(o=n);const s={type:e[u][1].type==="labelLink"?"link":"image",start:{...e[u][1].start},end:{...e[e.length-1][1].end}},a={type:"label",start:{...e[u][1].start},end:{...e[o][1].end}},f={type:"labelText",start:{...e[u+r+2][1].end},end:{...e[o-2][1].start}};return l=[["enter",s,t],["enter",a,t]],l=K(l,e.slice(u+1,u+r+3)),l=K(l,[["enter",f,t]]),l=K(l,Je(t.parser.constructs.insideSpan.null,e.slice(u+r+4,o-3),t)),l=K(l,[["exit",f,t],e[o-2],e[o-1],["exit",a,t]]),l=K(l,e.slice(o+1)),l=K(l,[["exit",s,t]]),re(e,u,e.length,l),e}function $r(e,t,n){const r=this;let i=r.events.length,u,o;for(;i--;)if((r.events[i][1].type==="labelImage"||r.events[i][1].type==="labelLink")&&!r.events[i][1]._balanced){u=r.events[i][1];break}return l;function l(d){return u?u._inactive?h(d):(o=r.parser.defined.includes(ge(r.sliceSerialize({start:u.end,end:r.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(d),e.exit("labelMarker"),e.exit("labelEnd"),s):n(d)}function s(d){return d===40?e.attempt(Hr,f,o?f:h)(d):d===91?e.attempt(jr,f,o?a:h)(d):o?f(d):h(d)}function a(d){return e.attempt(vr,f,h)(d)}function f(d){return t(d)}function h(d){return u._balanced=!0,n(d)}}function Wr(e,t,n){return r;function r(h){return e.enter("resource"),e.enter("resourceMarker"),e.consume(h),e.exit("resourceMarker"),i}function i(h){return $(h)?Se(e,u)(h):u(h)}function u(h){return h===41?f(h):Hn(e,o,l,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(h)}function o(h){return $(h)?Se(e,s)(h):f(h)}function l(h){return n(h)}function s(h){return h===34||h===39||h===40?vn(e,a,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(h):f(h)}function a(h){return $(h)?Se(e,f)(h):f(h)}function f(h){return h===41?(e.enter("resourceMarker"),e.consume(h),e.exit("resourceMarker"),e.exit("resource"),t):n(h)}}function Yr(e,t,n){const r=this;return i;function i(l){return jn.call(r,e,u,o,"reference","referenceMarker","referenceString")(l)}function u(l){return r.parser.defined.includes(ge(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(l):n(l)}function o(l){return n(l)}}function Kr(e,t,n){return r;function r(u){return e.enter("reference"),e.enter("referenceMarker"),e.consume(u),e.exit("referenceMarker"),i}function i(u){return u===93?(e.enter("referenceMarker"),e.consume(u),e.exit("referenceMarker"),e.exit("reference"),t):n(u)}}const Zr={name:"labelStartImage",resolveAll:Xe.resolveAll,tokenize:Gr};function Gr(e,t,n){const r=this;return i;function i(l){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(l),e.exit("labelImageMarker"),u}function u(l){return l===91?(e.enter("labelMarker"),e.consume(l),e.exit("labelMarker"),e.exit("labelImage"),o):n(l)}function o(l){return l===94&&"_hiddenFootnoteSupport"in r.parser.constructs?n(l):t(l)}}const Jr={name:"labelStartLink",resolveAll:Xe.resolveAll,tokenize:Xr};function Xr(e,t,n){const r=this;return i;function i(o){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(o),e.exit("labelMarker"),e.exit("labelLink"),u}function u(o){return o===94&&"_hiddenFootnoteSupport"in r.parser.constructs?n(o):t(o)}}const He={name:"lineEnding",tokenize:ei};function ei(e,t){return n;function n(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),B(e,t,"linePrefix")}}const Ae={name:"thematicBreak",tokenize:ni};function ni(e,t,n){let r=0,i;return u;function u(a){return e.enter("thematicBreak"),o(a)}function o(a){return i=a,l(a)}function l(a){return a===i?(e.enter("thematicBreakSequence"),s(a)):r>=3&&(a===null||I(a))?(e.exit("thematicBreak"),t(a)):n(a)}function s(a){return a===i?(e.consume(a),r++,s):(e.exit("thematicBreakSequence"),_(a)?B(e,l,"whitespace")(a):l(a))}}const Q={continuation:{tokenize:ui},exit:li,name:"list",tokenize:ii},ti={partial:!0,tokenize:ai},ri={partial:!0,tokenize:oi};function ii(e,t,n){const r=this,i=r.events[r.events.length-1];let u=i&&i[1].type==="linePrefix"?i[2].sliceSerialize(i[1],!0).length:0,o=0;return l;function l(c){const E=r.containerState.type||(c===42||c===43||c===45?"listUnordered":"listOrdered");if(E==="listUnordered"?!r.containerState.marker||c===r.containerState.marker:$e(c)){if(r.containerState.type||(r.containerState.type=E,e.enter(E,{_container:!0})),E==="listUnordered")return e.enter("listItemPrefix"),c===42||c===45?e.check(Ae,n,a)(c):a(c);if(!r.interrupt||c===49)return e.enter("listItemPrefix"),e.enter("listItemValue"),s(c)}return n(c)}function s(c){return $e(c)&&++o<10?(e.consume(c),s):(!r.interrupt||o<2)&&(r.containerState.marker?c===r.containerState.marker:c===41||c===46)?(e.exit("listItemValue"),a(c)):n(c)}function a(c){return e.enter("listItemMarker"),e.consume(c),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||c,e.check(Pe,r.interrupt?n:f,e.attempt(ti,d,h))}function f(c){return r.containerState.initialBlankLine=!0,u++,d(c)}function h(c){return _(c)?(e.enter("listItemPrefixWhitespace"),e.consume(c),e.exit("listItemPrefixWhitespace"),d):n(c)}function d(c){return r.containerState.size=u+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(c)}}function ui(e,t,n){const r=this;return r.containerState._closeFlow=void 0,e.check(Pe,i,u);function i(l){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,B(e,t,"listItemIndent",r.containerState.size+1)(l)}function u(l){return r.containerState.furtherBlankLines||!_(l)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,o(l)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(ri,t,o)(l))}function o(l){return r.containerState._closeFlow=!0,r.interrupt=void 0,B(e,e.attempt(Q,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(l)}}function oi(e,t,n){const r=this;return B(e,i,"listItemIndent",r.containerState.size+1);function i(u){const o=r.events[r.events.length-1];return o&&o[1].type==="listItemIndent"&&o[2].sliceSerialize(o[1],!0).length===r.containerState.size?t(u):n(u)}}function li(e){e.exit(this.containerState.type)}function ai(e,t,n){const r=this;return B(e,i,"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4+1);function i(u){const o=r.events[r.events.length-1];return!_(u)&&o&&o[1].type==="listItemPrefixWhitespace"?t(u):n(u)}}const _n={name:"setextUnderline",resolveTo:si,tokenize:ci};function si(e,t){let n=e.length,r,i,u;for(;n--;)if(e[n][0]==="enter"){if(e[n][1].type==="content"){r=n;break}e[n][1].type==="paragraph"&&(i=n)}else e[n][1].type==="content"&&e.splice(n,1),!u&&e[n][1].type==="definition"&&(u=n);const o={type:"setextHeading",start:{...e[r][1].start},end:{...e[e.length-1][1].end}};return e[i][1].type="setextHeadingText",u?(e.splice(i,0,["enter",o,t]),e.splice(u+1,0,["exit",e[r][1],t]),e[r][1].end={...e[u][1].end}):e[r][1]=o,e.push(["exit",o,t]),e}function ci(e,t,n){const r=this;let i;return u;function u(a){let f=r.events.length,h;for(;f--;)if(r.events[f][1].type!=="lineEnding"&&r.events[f][1].type!=="linePrefix"&&r.events[f][1].type!=="content"){h=r.events[f][1].type==="paragraph";break}return!r.parser.lazy[r.now().line]&&(r.interrupt||h)?(e.enter("setextHeadingLine"),i=a,o(a)):n(a)}function o(a){return e.enter("setextHeadingLineSequence"),l(a)}function l(a){return a===i?(e.consume(a),l):(e.exit("setextHeadingLineSequence"),_(a)?B(e,s,"lineSuffix")(a):s(a))}function s(a){return a===null||I(a)?(e.exit("setextHeadingLine"),t(a)):n(a)}}const fi={tokenize:hi};function hi(e){const t=this,n=e.attempt(Pe,r,e.attempt(this.parser.constructs.flowInitial,i,B(e,e.attempt(this.parser.constructs.flow,i,e.attempt(xr,i)),"linePrefix")));return n;function r(u){if(u===null){e.consume(u);return}return e.enter("lineEndingBlank"),e.consume(u),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n}function i(u){if(u===null){e.consume(u);return}return e.enter("lineEnding"),e.consume(u),e.exit("lineEnding"),t.currentConstruct=void 0,n}}const pi={resolveAll:Qn()},mi=Un("string"),di=Un("text");function Un(e){return{resolveAll:Qn(e==="text"?gi:void 0),tokenize:t};function t(n){const r=this,i=this.parser.constructs[e],u=n.attempt(i,o,l);return o;function o(f){return a(f)?u(f):l(f)}function l(f){if(f===null){n.consume(f);return}return n.enter("data"),n.consume(f),s}function s(f){return a(f)?(n.exit("data"),u(f)):(n.consume(f),s)}function a(f){if(f===null)return!0;const h=i[f];let d=-1;if(h)for(;++d<h.length;){const c=h[d];if(!c.previous||c.previous.call(r,r.previous))return!0}return!1}}}function Qn(e){return t;function t(n,r){let i=-1,u;for(;++i<=n.length;)u===void 0?n[i]&&n[i][1].type==="data"&&(u=i,i++):(!n[i]||n[i][1].type!=="data")&&(i!==u+2&&(n[u][1].end=n[i-1][1].end,n.splice(u+2,i-u-2),i=u+2),u=void 0);return e?e(n,r):n}}function gi(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||e[n][1].type==="lineEnding")&&e[n-1][1].type==="data"){const r=e[n-1][1],i=t.sliceStream(r);let u=i.length,o=-1,l=0,s;for(;u--;){const a=i[u];if(typeof a=="string"){for(o=a.length;a.charCodeAt(o-1)===32;)l++,o--;if(o)break;o=-1}else if(a===-2)s=!0,l++;else if(a!==-1){u++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(l=0),l){const a={type:n===e.length||s||l<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:u?o:r.start._bufferIndex+o,_index:r.start._index+u,line:r.end.line,column:r.end.column-l,offset:r.end.offset-l},end:{...r.end}};r.end={...a.start},r.start.offset===r.end.offset?Object.assign(r,a):(e.splice(n,0,["enter",a,t],["exit",a,t]),n+=2)}n++}return e}const xi={42:Q,43:Q,45:Q,48:Q,49:Q,50:Q,51:Q,52:Q,53:Q,54:Q,55:Q,56:Q,57:Q,62:Nn},ki={91:Sr},yi={[-2]:Ve,[-1]:Ve,32:Ve},bi={35:Ar,42:Ae,45:[_n,Ae],60:Lr,61:_n,95:Ae,96:Tn,126:Tn},wi={38:qn,92:Dn},Si={[-5]:He,[-4]:He,[-3]:He,33:Zr,38:qn,42:We,60:[Jt,qr],91:Jr,92:[zr,Dn],93:Xe,95:We,96:fr},Ii={null:[We,pi]},Ei={null:[42,95]},Ci={null:[]},zi=Object.freeze(Object.defineProperty({__proto__:null,attentionMarkers:Ei,contentInitial:ki,disable:Ci,document:xi,flow:bi,flowInitial:yi,insideSpan:Ii,string:wi,text:Si},Symbol.toStringTag,{value:"Module"}));function Ti(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0};const i={},u=[];let o=[],l=[];const s={attempt:F(q),check:F(x),consume:M,enter:b,exit:N,interrupt:F(x,{interrupt:!0})},a={code:null,containerState:{},defineSkip:z,events:[],now:E,parser:e,previous:null,sliceSerialize:d,sliceStream:c,write:h};let f=t.tokenize.call(a,s);return t.resolveAll&&u.push(t),a;function h(S){return o=K(o,S),R(),o[o.length-1]!==null?[]:(V(t,0),a.events=Je(u,a.events,a),a.events)}function d(S,w){return _i(c(S),w)}function c(S){return Ai(o,S)}function E(){const{_bufferIndex:S,_index:w,line:D,column:H,offset:A}=r;return{_bufferIndex:S,_index:w,line:D,column:H,offset:A}}function z(S){i[S.line]=S.column,O()}function R(){let S;for(;r._index<o.length;){const w=o[r._index];if(typeof w=="string")for(S=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===S&&r._bufferIndex<w.length;)y(w.charCodeAt(r._bufferIndex));else y(w)}}function y(S){f=f(S)}function M(S){I(S)?(r.line++,r.column=1,r.offset+=S===-3?2:1,O()):S!==-1&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===o[r._index].length&&(r._bufferIndex=-1,r._index++)),a.previous=S}function b(S,w){const D=w||{};return D.type=S,D.start=E(),a.events.push(["enter",D,a]),l.push(D),D}function N(S){const w=l.pop();return w.end=E(),a.events.push(["exit",w,a]),w}function q(S,w){V(S,w.from)}function x(S,w){w.restore()}function F(S,w){return D;function D(H,A,U){let j,Z,ie,p;return Array.isArray(H)?ue(H):"tokenize"in H?ue([H]):G(H);function G(v){return xe;function xe(ae){const pe=ae!==null&&v[ae],me=ae!==null&&v.null,Ce=[...Array.isArray(pe)?pe:pe?[pe]:[],...Array.isArray(me)?me:me?[me]:[]];return ue(Ce)(ae)}}function ue(v){return j=v,Z=0,v.length===0?U:m(v[Z])}function m(v){return xe;function xe(ae){return p=P(),ie=v,v.partial||(a.currentConstruct=v),v.name&&a.parser.constructs.disable.null.includes(v.name)?fe():v.tokenize.call(w?Object.assign(Object.create(a),w):a,s,J,fe)(ae)}}function J(v){return S(ie,p),A}function fe(v){return p.restore(),++Z<j.length?m(j[Z]):U}}}function V(S,w){S.resolveAll&&!u.includes(S)&&u.push(S),S.resolve&&re(a.events,w,a.events.length-w,S.resolve(a.events.slice(w),a)),S.resolveTo&&(a.events=S.resolveTo(a.events,a))}function P(){const S=E(),w=a.previous,D=a.currentConstruct,H=a.events.length,A=Array.from(l);return{from:H,restore:U};function U(){r=S,a.previous=w,a.currentConstruct=D,a.events.length=H,l=A,O()}}function O(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}function Ai(e,t){const n=t.start._index,r=t.start._bufferIndex,i=t.end._index,u=t.end._bufferIndex;let o;if(n===i)o=[e[n].slice(r,u)];else{if(o=e.slice(n,i),r>-1){const l=o[0];typeof l=="string"?o[0]=l.slice(r):o.shift()}u>0&&o.push(e[i].slice(0,u))}return o}function _i(e,t){let n=-1;const r=[];let i;for(;++n<e.length;){const u=e[n];let o;if(typeof u=="string")o=u;else switch(u){case-5:{o="\r";break}case-4:{o=`
`;break}case-3:{o=`\r
`;break}case-2:{o=t?" ":"	";break}case-1:{if(!t&&i)continue;o=" ";break}default:o=String.fromCharCode(u)}i=u===-2,r.push(o)}return r.join("")}function Fi(e){const r={constructs:Nt([zi,...(e||{}).extensions||[]]),content:i(Qt),defined:[],document:i(Wt),flow:i(fi),lazy:{},string:i(mi),text:i(di)};return r;function i(u){return o;function o(l){return Ti(r,u,l)}}}function Pi(e){for(;!Vn(e););return e}const Fn=/[\0\t\n\r]/g;function Li(){let e=1,t="",n=!0,r;return i;function i(u,o,l){const s=[];let a,f,h,d,c;for(u=t+(typeof u=="string"?u.toString():new TextDecoder(o||void 0).decode(u)),h=0,t="",n&&(u.charCodeAt(0)===65279&&h++,n=void 0);h<u.length;){if(Fn.lastIndex=h,a=Fn.exec(u),d=a&&a.index!==void 0?a.index:u.length,c=u.charCodeAt(d),!a){t=u.slice(h);break}if(c===10&&h===d&&r)s.push(-3),r=void 0;else switch(r&&(s.push(-5),r=void 0),h<d&&(s.push(u.slice(h,d)),e+=d-h),c){case 0:{s.push(65533),e++;break}case 9:{for(f=Math.ceil(e/4)*4,s.push(-2);e++<f;)s.push(-1);break}case 10:{s.push(-4),e=1;break}default:r=!0,e=1}h=d+1}return l&&(r&&s.push(-5),t&&s.push(t),s.push(null)),s}}const Bi=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function Ri(e){return e.replace(Bi,Oi)}function Oi(e,t,n){if(t)return t;if(n.charCodeAt(0)===35){const i=n.charCodeAt(1),u=i===120||i===88;return Mn(n.slice(u?2:1),u?16:10)}return Ge(n)||e}const $n={}.hasOwnProperty;function Mi(e,t,n){return typeof t!="string"&&(n=t,t=void 0),Ni(n)(Pi(Fi(n).document().write(Li()(e,t,!0))))}function Ni(e){const t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:u(ln),autolinkProtocol:P,autolinkEmail:P,atxHeading:u(rn),blockQuote:u(me),characterEscape:P,characterReference:P,codeFenced:u(Ce),codeFencedFenceInfo:o,codeFencedFenceMeta:o,codeIndented:u(Ce,o),codeText:u(ut,o),codeTextData:P,data:P,codeFlowValue:P,definition:u(ot),definitionDestinationString:o,definitionLabelString:o,definitionTitleString:o,emphasis:u(lt),hardBreakEscape:u(un),hardBreakTrailing:u(un),htmlFlow:u(on,o),htmlFlowData:P,htmlText:u(on,o),htmlTextData:P,image:u(at),label:o,link:u(ln),listItem:u(st),listItemValue:d,listOrdered:u(an,h),listUnordered:u(an),paragraph:u(ct),reference:m,referenceString:o,resourceDestinationString:o,resourceTitleString:o,setextHeading:u(rn),strong:u(ft),thematicBreak:u(pt)},exit:{atxHeading:s(),atxHeadingSequence:q,autolink:s(),autolinkEmail:pe,autolinkProtocol:ae,blockQuote:s(),characterEscapeValue:O,characterReferenceMarkerHexadecimal:fe,characterReferenceMarkerNumeric:fe,characterReferenceValue:v,characterReference:xe,codeFenced:s(R),codeFencedFence:z,codeFencedFenceInfo:c,codeFencedFenceMeta:E,codeFlowValue:O,codeIndented:s(y),codeText:s(A),codeTextData:O,data:O,definition:s(),definitionDestinationString:N,definitionLabelString:M,definitionTitleString:b,emphasis:s(),hardBreakEscape:s(w),hardBreakTrailing:s(w),htmlFlow:s(D),htmlFlowData:O,htmlText:s(H),htmlTextData:O,image:s(j),label:ie,labelText:Z,lineEnding:S,link:s(U),listItem:s(),listOrdered:s(),listUnordered:s(),paragraph:s(),referenceString:J,resourceDestinationString:p,resourceTitleString:G,resource:ue,setextHeading:s(V),setextHeadingLineSequence:F,setextHeadingText:x,strong:s(),thematicBreak:s()}};Wn(t,(e||{}).mdastExtensions||[]);const n={};return r;function r(g){let k={type:"root",children:[]};const C={stack:[k],tokenStack:[],config:t,enter:l,exit:a,buffer:o,resume:f,data:n},T=[];let L=-1;for(;++L<g.length;)if(g[L][1].type==="listOrdered"||g[L][1].type==="listUnordered")if(g[L][0]==="enter")T.push(L);else{const X=T.pop();L=i(g,X,L)}for(L=-1;++L<g.length;){const X=t[g[L][0]];$n.call(X,g[L][1].type)&&X[g[L][1].type].call(Object.assign({sliceSerialize:g[L][2].sliceSerialize},C),g[L][1])}if(C.tokenStack.length>0){const X=C.tokenStack[C.tokenStack.length-1];(X[1]||Pn).call(C,void 0,X[0])}for(k.position={start:se(g.length>0?g[0][1].start:{line:1,column:1,offset:0}),end:se(g.length>0?g[g.length-2][1].end:{line:1,column:1,offset:0})},L=-1;++L<t.transforms.length;)k=t.transforms[L](k)||k;return k}function i(g,k,C){let T=k-1,L=-1,X=!1,he,oe,ke,ye;for(;++T<=C;){const Y=g[T];switch(Y[1].type){case"listUnordered":case"listOrdered":case"blockQuote":{Y[0]==="enter"?L++:L--,ye=void 0;break}case"lineEndingBlank":{Y[0]==="enter"&&(he&&!ye&&!L&&!ke&&(ke=T),ye=void 0);break}case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:ye=void 0}if(!L&&Y[0]==="enter"&&Y[1].type==="listItemPrefix"||L===-1&&Y[0]==="exit"&&(Y[1].type==="listUnordered"||Y[1].type==="listOrdered")){if(he){let de=T;for(oe=void 0;de--;){const le=g[de];if(le[1].type==="lineEnding"||le[1].type==="lineEndingBlank"){if(le[0]==="exit")continue;oe&&(g[oe][1].type="lineEndingBlank",X=!0),le[1].type="lineEnding",oe=de}else if(!(le[1].type==="linePrefix"||le[1].type==="blockQuotePrefix"||le[1].type==="blockQuotePrefixWhitespace"||le[1].type==="blockQuoteMarker"||le[1].type==="listItemIndent"))break}ke&&(!oe||ke<oe)&&(he._spread=!0),he.end=Object.assign({},oe?g[oe][1].start:Y[1].end),g.splice(oe||T,0,["exit",he,Y[2]]),T++,C++}if(Y[1].type==="listItemPrefix"){const de={type:"listItem",_spread:!1,start:Object.assign({},Y[1].start),end:void 0};he=de,g.splice(T,0,["enter",de,Y[2]]),T++,C++,ke=void 0,ye=!0}}}return g[k][1]._spread=X,C}function u(g,k){return C;function C(T){l.call(this,g(T),T),k&&k.call(this,T)}}function o(){this.stack.push({type:"fragment",children:[]})}function l(g,k,C){this.stack[this.stack.length-1].children.push(g),this.stack.push(g),this.tokenStack.push([k,C||void 0]),g.position={start:se(k.start),end:void 0}}function s(g){return k;function k(C){g&&g.call(this,C),a.call(this,C)}}function a(g,k){const C=this.stack.pop(),T=this.tokenStack.pop();if(T)T[0].type!==g.type&&(k?k.call(this,g,T[0]):(T[1]||Pn).call(this,g,T[0]));else throw new Error("Cannot close `"+g.type+"` ("+we({start:g.start,end:g.end})+"): it’s not open");C.position.end=se(g.end)}function f(){return Ze(this.stack.pop())}function h(){this.data.expectingFirstListItemValue=!0}function d(g){if(this.data.expectingFirstListItemValue){const k=this.stack[this.stack.length-2];k.start=Number.parseInt(this.sliceSerialize(g),10),this.data.expectingFirstListItemValue=void 0}}function c(){const g=this.resume(),k=this.stack[this.stack.length-1];k.lang=g}function E(){const g=this.resume(),k=this.stack[this.stack.length-1];k.meta=g}function z(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function R(){const g=this.resume(),k=this.stack[this.stack.length-1];k.value=g.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function y(){const g=this.resume(),k=this.stack[this.stack.length-1];k.value=g.replace(/(\r?\n|\r)$/g,"")}function M(g){const k=this.resume(),C=this.stack[this.stack.length-1];C.label=k,C.identifier=ge(this.sliceSerialize(g)).toLowerCase()}function b(){const g=this.resume(),k=this.stack[this.stack.length-1];k.title=g}function N(){const g=this.resume(),k=this.stack[this.stack.length-1];k.url=g}function q(g){const k=this.stack[this.stack.length-1];if(!k.depth){const C=this.sliceSerialize(g).length;k.depth=C}}function x(){this.data.setextHeadingSlurpLineEnding=!0}function F(g){const k=this.stack[this.stack.length-1];k.depth=this.sliceSerialize(g).codePointAt(0)===61?1:2}function V(){this.data.setextHeadingSlurpLineEnding=void 0}function P(g){const C=this.stack[this.stack.length-1].children;let T=C[C.length-1];(!T||T.type!=="text")&&(T=ht(),T.position={start:se(g.start),end:void 0},C.push(T)),this.stack.push(T)}function O(g){const k=this.stack.pop();k.value+=this.sliceSerialize(g),k.position.end=se(g.end)}function S(g){const k=this.stack[this.stack.length-1];if(this.data.atHardBreak){const C=k.children[k.children.length-1];C.position.end=se(g.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(k.type)&&(P.call(this,g),O.call(this,g))}function w(){this.data.atHardBreak=!0}function D(){const g=this.resume(),k=this.stack[this.stack.length-1];k.value=g}function H(){const g=this.resume(),k=this.stack[this.stack.length-1];k.value=g}function A(){const g=this.resume(),k=this.stack[this.stack.length-1];k.value=g}function U(){const g=this.stack[this.stack.length-1];if(this.data.inReference){const k=this.data.referenceType||"shortcut";g.type+="Reference",g.referenceType=k,delete g.url,delete g.title}else delete g.identifier,delete g.label;this.data.referenceType=void 0}function j(){const g=this.stack[this.stack.length-1];if(this.data.inReference){const k=this.data.referenceType||"shortcut";g.type+="Reference",g.referenceType=k,delete g.url,delete g.title}else delete g.identifier,delete g.label;this.data.referenceType=void 0}function Z(g){const k=this.sliceSerialize(g),C=this.stack[this.stack.length-2];C.label=Ri(k),C.identifier=ge(k).toLowerCase()}function ie(){const g=this.stack[this.stack.length-1],k=this.resume(),C=this.stack[this.stack.length-1];if(this.data.inReference=!0,C.type==="link"){const T=g.children;C.children=T}else C.alt=k}function p(){const g=this.resume(),k=this.stack[this.stack.length-1];k.url=g}function G(){const g=this.resume(),k=this.stack[this.stack.length-1];k.title=g}function ue(){this.data.inReference=void 0}function m(){this.data.referenceType="collapsed"}function J(g){const k=this.resume(),C=this.stack[this.stack.length-1];C.label=k,C.identifier=ge(this.sliceSerialize(g)).toLowerCase(),this.data.referenceType="full"}function fe(g){this.data.characterReferenceType=g.type}function v(g){const k=this.sliceSerialize(g),C=this.data.characterReferenceType;let T;C?(T=Mn(k,C==="characterReferenceMarkerNumeric"?10:16),this.data.characterReferenceType=void 0):T=Ge(k);const L=this.stack[this.stack.length-1];L.value+=T}function xe(g){const k=this.stack.pop();k.position.end=se(g.end)}function ae(g){O.call(this,g);const k=this.stack[this.stack.length-1];k.url=this.sliceSerialize(g)}function pe(g){O.call(this,g);const k=this.stack[this.stack.length-1];k.url="mailto:"+this.sliceSerialize(g)}function me(){return{type:"blockquote",children:[]}}function Ce(){return{type:"code",lang:null,meta:null,value:""}}function ut(){return{type:"inlineCode",value:""}}function ot(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function lt(){return{type:"emphasis",children:[]}}function rn(){return{type:"heading",depth:0,children:[]}}function un(){return{type:"break"}}function on(){return{type:"html",value:""}}function at(){return{type:"image",title:null,url:"",alt:null}}function ln(){return{type:"link",title:null,url:"",children:[]}}function an(g){return{type:"list",ordered:g.type==="listOrdered",start:null,spread:g._spread,children:[]}}function st(g){return{type:"listItem",spread:g._spread,checked:null,children:[]}}function ct(){return{type:"paragraph",children:[]}}function ft(){return{type:"strong",children:[]}}function ht(){return{type:"text",value:""}}function pt(){return{type:"thematicBreak"}}}function se(e){return{line:e.line,column:e.column,offset:e.offset}}function Wn(e,t){let n=-1;for(;++n<t.length;){const r=t[n];Array.isArray(r)?Wn(e,r):Di(e,r)}}function Di(e,t){let n;for(n in t)if($n.call(t,n))switch(n){case"canContainEols":{const r=t[n];r&&e[n].push(...r);break}case"transforms":{const r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{const r=t[n];r&&Object.assign(e[n],r);break}}}function Pn(e,t){throw e?new Error("Cannot close `"+e.type+"` ("+we({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+we({start:t.start,end:t.end})+") is open"):new Error("Cannot close document, a token (`"+t.type+"`, "+we({start:t.start,end:t.end})+") is still open")}function Fu(e){const t=this;t.parser=n;function n(r){return Mi(r,{...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})}}function Pu(e,t){const n=String(e);if(typeof t!="string")throw new TypeError("Expected character");let r=0,i=n.indexOf(t);for(;i!==-1;)r++,i=n.indexOf(t,i+t.length);return r}const en=function(e){if(e==null)return ji;if(typeof e=="function")return Le(e);if(typeof e=="object")return Array.isArray(e)?qi(e):Vi(e);if(typeof e=="string")return Hi(e);throw new Error("Expected function, string, or object as test")};function qi(e){const t=[];let n=-1;for(;++n<e.length;)t[n]=en(e[n]);return Le(r);function r(...i){let u=-1;for(;++u<t.length;)if(t[u].apply(this,i))return!0;return!1}}function Vi(e){const t=e;return Le(n);function n(r){const i=r;let u;for(u in e)if(i[u]!==t[u])return!1;return!0}}function Hi(e){return Le(t);function t(n){return n&&n.type===e}}function Le(e){return t;function t(n,r,i){return!!(vi(n)&&e.call(this,n,typeof r=="number"?r:void 0,i||void 0))}}function ji(){return!0}function vi(e){return e!==null&&typeof e=="object"&&"type"in e}const Yn=[],Ui=!0,Ye=!1,Qi="skip";function $i(e,t,n,r){let i;typeof t=="function"&&typeof n!="function"?(r=n,n=t):i=t;const u=en(i),o=r?-1:1;l(e,void 0,[])();function l(s,a,f){const h=s&&typeof s=="object"?s:{};if(typeof h.type=="string"){const c=typeof h.tagName=="string"?h.tagName:typeof h.name=="string"?h.name:void 0;Object.defineProperty(d,"name",{value:"node ("+(s.type+(c?"<"+c+">":""))+")"})}return d;function d(){let c=Yn,E,z,R;if((!t||u(s,a,f[f.length-1]||void 0))&&(c=Wi(n(s,f)),c[0]===Ye))return c;if("children"in s&&s.children){const y=s;if(y.children&&c[0]!==Qi)for(z=(r?y.children.length:-1)+o,R=f.concat(y);z>-1&&z<y.children.length;){const M=y.children[z];if(E=l(M,z,R)(),E[0]===Ye)return E;z=typeof E[1]=="number"?E[1]:z+o}}return c}}}function Wi(e){return Array.isArray(e)?e:typeof e=="number"?[Ui,e]:e==null?Yn:[e]}function Yi(e,t,n,r){const i=n.enter("blockquote"),u=n.createTracker(r);u.move("> "),u.shift(2);const o=n.indentLines(n.containerFlow(e,u.current()),Ki);return i(),o}function Ki(e,t,n){return">"+(n?"":" ")+e}function Zi(e,t){return Ln(e,t.inConstruct,!0)&&!Ln(e,t.notInConstruct,!1)}function Ln(e,t,n){if(typeof t=="string"&&(t=[t]),!t||t.length===0)return n;let r=-1;for(;++r<t.length;)if(e.includes(t[r]))return!0;return!1}function Bn(e,t,n,r){let i=-1;for(;++i<n.unsafe.length;)if(n.unsafe[i].character===`
`&&Zi(n.stack,n.unsafe[i]))return/[ \t]/.test(r.before)?"":" ";return`\\
`}function Gi(e,t){const n=String(e);let r=n.indexOf(t),i=r,u=0,o=0;if(typeof t!="string")throw new TypeError("Expected substring");for(;r!==-1;)r===i?++u>o&&(o=u):u=1,i=r+t.length,r=n.indexOf(t,i);return o}function Ji(e,t){return!!(t.options.fences===!1&&e.value&&!e.lang&&/[^ \r\n]/.test(e.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(e.value))}function Xi(e){const t=e.options.fence||"`";if(t!=="`"&&t!=="~")throw new Error("Cannot serialize code with `"+t+"` for `options.fence`, expected `` ` `` or `~`");return t}function eu(e,t,n,r){const i=Xi(n),u=e.value||"",o=i==="`"?"GraveAccent":"Tilde";if(Ji(e,n)){const h=n.enter("codeIndented"),d=n.indentLines(u,nu);return h(),d}const l=n.createTracker(r),s=i.repeat(Math.max(Gi(u,i)+1,3)),a=n.enter("codeFenced");let f=l.move(s);if(e.lang){const h=n.enter(`codeFencedLang${o}`);f+=l.move(n.safe(e.lang,{before:f,after:" ",encode:["`"],...l.current()})),h()}if(e.lang&&e.meta){const h=n.enter(`codeFencedMeta${o}`);f+=l.move(" "),f+=l.move(n.safe(e.meta,{before:f,after:`
`,encode:["`"],...l.current()})),h()}return f+=l.move(`
`),u&&(f+=l.move(u+`
`)),f+=l.move(s),a(),f}function nu(e,t,n){return(n?"":"    ")+e}function nn(e){const t=e.options.quote||'"';if(t!=='"'&&t!=="'")throw new Error("Cannot serialize title with `"+t+"` for `options.quote`, expected `\"`, or `'`");return t}function tu(e,t,n,r){const i=nn(n),u=i==='"'?"Quote":"Apostrophe",o=n.enter("definition");let l=n.enter("label");const s=n.createTracker(r);let a=s.move("[");return a+=s.move(n.safe(n.associationId(e),{before:a,after:"]",...s.current()})),a+=s.move("]: "),l(),!e.url||/[\0- \u007F]/.test(e.url)?(l=n.enter("destinationLiteral"),a+=s.move("<"),a+=s.move(n.safe(e.url,{before:a,after:">",...s.current()})),a+=s.move(">")):(l=n.enter("destinationRaw"),a+=s.move(n.safe(e.url,{before:a,after:e.title?" ":`
`,...s.current()}))),l(),e.title&&(l=n.enter(`title${u}`),a+=s.move(" "+i),a+=s.move(n.safe(e.title,{before:a,after:i,...s.current()})),a+=s.move(i),l()),o(),a}function ru(e){const t=e.options.emphasis||"*";if(t!=="*"&&t!=="_")throw new Error("Cannot serialize emphasis with `"+t+"` for `options.emphasis`, expected `*`, or `_`");return t}function Ie(e){return"&#x"+e.toString(16).toUpperCase()+";"}function Fe(e,t,n){const r=_e(e),i=_e(t);return r===void 0?i===void 0?n==="_"?{inside:!0,outside:!0}:{inside:!1,outside:!1}:i===1?{inside:!0,outside:!0}:{inside:!1,outside:!0}:r===1?i===void 0?{inside:!1,outside:!1}:i===1?{inside:!0,outside:!0}:{inside:!1,outside:!1}:i===void 0?{inside:!1,outside:!1}:i===1?{inside:!0,outside:!1}:{inside:!1,outside:!1}}Kn.peek=iu;function Kn(e,t,n,r){const i=ru(n),u=n.enter("emphasis"),o=n.createTracker(r),l=o.move(i);let s=o.move(n.containerPhrasing(e,{after:i,before:l,...o.current()}));const a=s.charCodeAt(0),f=Fe(r.before.charCodeAt(r.before.length-1),a,i);f.inside&&(s=Ie(a)+s.slice(1));const h=s.charCodeAt(s.length-1),d=Fe(r.after.charCodeAt(0),h,i);d.inside&&(s=s.slice(0,-1)+Ie(h));const c=o.move(i);return u(),n.attentionEncodeSurroundingInfo={after:d.outside,before:f.outside},l+s+c}function iu(e,t,n){return n.options.emphasis||"*"}function uu(e,t,n,r){let i,u,o;typeof t=="function"&&typeof n!="function"?(u=void 0,o=t,i=n):(u=t,o=n,i=r),$i(e,u,l,i);function l(s,a){const f=a[a.length-1],h=f?f.children.indexOf(s):void 0;return o(s,h,f)}}function ou(e,t){let n=!1;return uu(e,function(r){if("value"in r&&/\r?\n|\r/.test(r.value)||r.type==="break")return n=!0,Ye}),!!((!e.depth||e.depth<3)&&Ze(e)&&(t.options.setext||n))}function lu(e,t,n,r){const i=Math.max(Math.min(6,e.depth||1),1),u=n.createTracker(r);if(ou(e,n)){const f=n.enter("headingSetext"),h=n.enter("phrasing"),d=n.containerPhrasing(e,{...u.current(),before:`
`,after:`
`});return h(),f(),d+`
`+(i===1?"=":"-").repeat(d.length-(Math.max(d.lastIndexOf("\r"),d.lastIndexOf(`
`))+1))}const o="#".repeat(i),l=n.enter("headingAtx"),s=n.enter("phrasing");u.move(o+" ");let a=n.containerPhrasing(e,{before:"# ",after:`
`,...u.current()});return/^[\t ]/.test(a)&&(a=Ie(a.charCodeAt(0))+a.slice(1)),a=a?o+" "+a:o,n.options.closeAtx&&(a+=" "+o),s(),l(),a}Zn.peek=au;function Zn(e){return e.value||""}function au(){return"<"}Gn.peek=su;function Gn(e,t,n,r){const i=nn(n),u=i==='"'?"Quote":"Apostrophe",o=n.enter("image");let l=n.enter("label");const s=n.createTracker(r);let a=s.move("![");return a+=s.move(n.safe(e.alt,{before:a,after:"]",...s.current()})),a+=s.move("]("),l(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(l=n.enter("destinationLiteral"),a+=s.move("<"),a+=s.move(n.safe(e.url,{before:a,after:">",...s.current()})),a+=s.move(">")):(l=n.enter("destinationRaw"),a+=s.move(n.safe(e.url,{before:a,after:e.title?" ":")",...s.current()}))),l(),e.title&&(l=n.enter(`title${u}`),a+=s.move(" "+i),a+=s.move(n.safe(e.title,{before:a,after:i,...s.current()})),a+=s.move(i),l()),a+=s.move(")"),o(),a}function su(){return"!"}Jn.peek=cu;function Jn(e,t,n,r){const i=e.referenceType,u=n.enter("imageReference");let o=n.enter("label");const l=n.createTracker(r);let s=l.move("![");const a=n.safe(e.alt,{before:s,after:"]",...l.current()});s+=l.move(a+"]["),o();const f=n.stack;n.stack=[],o=n.enter("reference");const h=n.safe(n.associationId(e),{before:s,after:"]",...l.current()});return o(),n.stack=f,u(),i==="full"||!a||a!==h?s+=l.move(h+"]"):i==="shortcut"?s=s.slice(0,-1):s+=l.move("]"),s}function cu(){return"!"}Xn.peek=fu;function Xn(e,t,n){let r=e.value||"",i="`",u=-1;for(;new RegExp("(^|[^`])"+i+"([^`]|$)").test(r);)i+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++u<n.unsafe.length;){const o=n.unsafe[u],l=n.compilePattern(o);let s;if(o.atBreak)for(;s=l.exec(r);){let a=s.index;r.charCodeAt(a)===10&&r.charCodeAt(a-1)===13&&a--,r=r.slice(0,a)+" "+r.slice(s.index+1)}}return i+r+i}function fu(){return"`"}function et(e,t){const n=Ze(e);return!!(!t.options.resourceLink&&e.url&&!e.title&&e.children&&e.children.length===1&&e.children[0].type==="text"&&(n===e.url||"mailto:"+n===e.url)&&/^[a-z][a-z+.-]+:/i.test(e.url)&&!/[\0- <>\u007F]/.test(e.url))}nt.peek=hu;function nt(e,t,n,r){const i=nn(n),u=i==='"'?"Quote":"Apostrophe",o=n.createTracker(r);let l,s;if(et(e,n)){const f=n.stack;n.stack=[],l=n.enter("autolink");let h=o.move("<");return h+=o.move(n.containerPhrasing(e,{before:h,after:">",...o.current()})),h+=o.move(">"),l(),n.stack=f,h}l=n.enter("link"),s=n.enter("label");let a=o.move("[");return a+=o.move(n.containerPhrasing(e,{before:a,after:"](",...o.current()})),a+=o.move("]("),s(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(s=n.enter("destinationLiteral"),a+=o.move("<"),a+=o.move(n.safe(e.url,{before:a,after:">",...o.current()})),a+=o.move(">")):(s=n.enter("destinationRaw"),a+=o.move(n.safe(e.url,{before:a,after:e.title?" ":")",...o.current()}))),s(),e.title&&(s=n.enter(`title${u}`),a+=o.move(" "+i),a+=o.move(n.safe(e.title,{before:a,after:i,...o.current()})),a+=o.move(i),s()),a+=o.move(")"),l(),a}function hu(e,t,n){return et(e,n)?"<":"["}tt.peek=pu;function tt(e,t,n,r){const i=e.referenceType,u=n.enter("linkReference");let o=n.enter("label");const l=n.createTracker(r);let s=l.move("[");const a=n.containerPhrasing(e,{before:s,after:"]",...l.current()});s+=l.move(a+"]["),o();const f=n.stack;n.stack=[],o=n.enter("reference");const h=n.safe(n.associationId(e),{before:s,after:"]",...l.current()});return o(),n.stack=f,u(),i==="full"||!a||a!==h?s+=l.move(h+"]"):i==="shortcut"?s=s.slice(0,-1):s+=l.move("]"),s}function pu(){return"["}function tn(e){const t=e.options.bullet||"*";if(t!=="*"&&t!=="+"&&t!=="-")throw new Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}function mu(e){const t=tn(e),n=e.options.bulletOther;if(!n)return t==="*"?"-":"*";if(n!=="*"&&n!=="+"&&n!=="-")throw new Error("Cannot serialize items with `"+n+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(n===t)throw new Error("Expected `bullet` (`"+t+"`) and `bulletOther` (`"+n+"`) to be different");return n}function du(e){const t=e.options.bulletOrdered||".";if(t!=="."&&t!==")")throw new Error("Cannot serialize items with `"+t+"` for `options.bulletOrdered`, expected `.` or `)`");return t}function rt(e){const t=e.options.rule||"*";if(t!=="*"&&t!=="-"&&t!=="_")throw new Error("Cannot serialize rules with `"+t+"` for `options.rule`, expected `*`, `-`, or `_`");return t}function gu(e,t,n,r){const i=n.enter("list"),u=n.bulletCurrent;let o=e.ordered?du(n):tn(n);const l=e.ordered?o==="."?")":".":mu(n);let s=t&&n.bulletLastUsed?o===n.bulletLastUsed:!1;if(!e.ordered){const f=e.children?e.children[0]:void 0;if((o==="*"||o==="-")&&f&&(!f.children||!f.children[0])&&n.stack[n.stack.length-1]==="list"&&n.stack[n.stack.length-2]==="listItem"&&n.stack[n.stack.length-3]==="list"&&n.stack[n.stack.length-4]==="listItem"&&n.indexStack[n.indexStack.length-1]===0&&n.indexStack[n.indexStack.length-2]===0&&n.indexStack[n.indexStack.length-3]===0&&(s=!0),rt(n)===o&&f){let h=-1;for(;++h<e.children.length;){const d=e.children[h];if(d&&d.type==="listItem"&&d.children&&d.children[0]&&d.children[0].type==="thematicBreak"){s=!0;break}}}}s&&(o=l),n.bulletCurrent=o;const a=n.containerFlow(e,r);return n.bulletLastUsed=o,n.bulletCurrent=u,i(),a}function xu(e){const t=e.options.listItemIndent||"one";if(t!=="tab"&&t!=="one"&&t!=="mixed")throw new Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}function ku(e,t,n,r){const i=xu(n);let u=n.bulletCurrent||tn(n);t&&t.type==="list"&&t.ordered&&(u=(typeof t.start=="number"&&t.start>-1?t.start:1)+(n.options.incrementListMarker===!1?0:t.children.indexOf(e))+u);let o=u.length+1;(i==="tab"||i==="mixed"&&(t&&t.type==="list"&&t.spread||e.spread))&&(o=Math.ceil(o/4)*4);const l=n.createTracker(r);l.move(u+" ".repeat(o-u.length)),l.shift(o);const s=n.enter("listItem"),a=n.indentLines(n.containerFlow(e,l.current()),f);return s(),a;function f(h,d,c){return d?(c?"":" ".repeat(o))+h:(c?u:u+" ".repeat(o-u.length))+h}}function yu(e,t,n,r){const i=n.enter("paragraph"),u=n.enter("phrasing"),o=n.containerPhrasing(e,r);return u(),i(),o}const bu=en(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function wu(e,t,n,r){return(e.children.some(function(o){return bu(o)})?n.containerPhrasing:n.containerFlow).call(n,e,r)}function Su(e){const t=e.options.strong||"*";if(t!=="*"&&t!=="_")throw new Error("Cannot serialize strong with `"+t+"` for `options.strong`, expected `*`, or `_`");return t}it.peek=Iu;function it(e,t,n,r){const i=Su(n),u=n.enter("strong"),o=n.createTracker(r),l=o.move(i+i);let s=o.move(n.containerPhrasing(e,{after:i,before:l,...o.current()}));const a=s.charCodeAt(0),f=Fe(r.before.charCodeAt(r.before.length-1),a,i);f.inside&&(s=Ie(a)+s.slice(1));const h=s.charCodeAt(s.length-1),d=Fe(r.after.charCodeAt(0),h,i);d.inside&&(s=s.slice(0,-1)+Ie(h));const c=o.move(i+i);return u(),n.attentionEncodeSurroundingInfo={after:d.outside,before:f.outside},l+s+c}function Iu(e,t,n){return n.options.strong||"*"}function Eu(e,t,n,r){return n.safe(e.value,r)}function Cu(e){const t=e.options.ruleRepetition||3;if(t<3)throw new Error("Cannot serialize rules with repetition `"+t+"` for `options.ruleRepetition`, expected `3` or more");return t}function zu(e,t,n){const r=(rt(n)+(n.options.ruleSpaces?" ":"")).repeat(Cu(n));return n.options.ruleSpaces?r.slice(0,-1):r}const Lu={blockquote:Yi,break:Bn,code:eu,definition:tu,emphasis:Kn,hardBreak:Bn,heading:lu,html:Zn,image:Gn,imageReference:Jn,inlineCode:Xn,link:nt,linkReference:tt,list:gu,listItem:ku,paragraph:yu,root:wu,strong:it,text:Eu,thematicBreak:zu};export{Pu as a,vt as b,en as c,ee as d,te as e,Qe as f,B as g,Lu as h,Pe as i,_e as j,I as k,_ as l,$ as m,ge as n,Au as o,Nt as p,Gi as q,Je as r,re as s,_u as t,Ut as u,$i as v,Fu as w,uu as x};
//# sourceMappingURL=markdown-51287e2f.js.map
