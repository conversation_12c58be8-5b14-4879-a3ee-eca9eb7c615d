<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Hierarchical Markdown Visualization System - Transform your markdown documents into interactive visual explorations" />
    <meta name="keywords" content="markdown, visualization, documentation, tree, hierarchy" />
    <meta name="author" content="Markdown Visualizer" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Markdown Visualizer" />
    <meta property="og:description" content="Transform your markdown documents into interactive visual explorations" />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:title" content="Markdown Visualizer" />
    <meta property="twitter:description" content="Transform your markdown documents into interactive visual explorations" />
    
    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <title>Markdown Visualizer</title>
    <script type="module" crossorigin src="/assets/index-79a08009.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-27d6d7f2.js">
    <link rel="modulepreload" crossorigin href="/assets/markdown-51287e2f.js">
    <link rel="modulepreload" crossorigin href="/assets/d3-b3606c65.js">
    <link rel="stylesheet" href="/assets/index-cc1f00a0.css">
  </head>
  <body>
    <div id="root"></div>
    
  </body>
</html>
