// Documentation Analysis Module
// Analyzes all markdown files in the docs folder for comprehensive testing

const fs = require('fs')
const path = require('path')

const API_BASE_URL = 'http://localhost:3001'

// Analyze a single markdown file
function analyzeMarkdownFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8')
  const stats = fs.statSync(filePath)
  
  const analysis = {
    file: path.relative(process.cwd(), filePath),
    size: stats.size,
    content: content,
    
    // Basic metrics
    lines: content.split('\n').length,
    words: content.split(/\s+/).filter(word => word.length > 0).length,
    characters: content.length,
    
    // Heading structure
    headings: {
      h1: (content.match(/^# /gm) || []).length,
      h2: (content.match(/^## /gm) || []).length,
      h3: (content.match(/^### /gm) || []).length,
      h4: (content.match(/^#### /gm) || []).length,
      h5: (content.match(/^##### /gm) || []).length,
      h6: (content.match(/^###### /gm) || []).length,
    },
    
    // Content features
    features: {
      codeBlocks: (content.match(/```[\s\S]*?```/g) || []).length,
      inlineCode: (content.match(/`[^`]+`/g) || []).length,
      links: (content.match(/\[([^\]]+)\]\(([^)]+)\)/g) || []).length,
      images: (content.match(/!\[([^\]]*)\]\(([^)]+)\)/g) || []).length,
      tables: (content.match(/\|.*\|/g) || []).length,
      lists: (content.match(/^[\s]*[-*+]\s/gm) || []).length,
      orderedLists: (content.match(/^[\s]*\d+\.\s/gm) || []).length,
      blockquotes: (content.match(/^>/gm) || []).length,
      boldText: (content.match(/\*\*[^*]+\*\*/g) || []).length,
      italicText: (content.match(/\*[^*]+\*/g) || []).length,
    }
  }
  
  analysis.headings.total = Object.values(analysis.headings).reduce((sum, count) => sum + count, 0)
  analysis.complexity = analysis.headings.total > 20 ? 'high' : analysis.headings.total > 10 ? 'medium' : 'low'
  analysis.estimatedReadTime = Math.ceil(analysis.words / 200)
  
  return analysis
}

// Test API parser with a file
async function testAPIWithFile(content, fileName) {
  try {
    const response = await fetch(`${API_BASE_URL}/api/parse`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        content,
        options: { maxDepth: 6, includeContent: true, extractMetadata: true }
      })
    })
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`)
    }
    
    const data = await response.json()
    
    if (!data.success) {
      throw new Error(data.error?.message || 'Parse failed')
    }
    
    return {
      success: true,
      fileName,
      nodeCount: data.data.statistics.nodeCount,
      maxDepth: data.data.statistics.maxDepth,
      title: data.data.metadata.title,
      wordCount: data.data.metadata.totalWordCount,
      hasCode: data.data.metadata.hasCode,
      hasImages: data.data.metadata.hasImages,
      complexity: data.data.metadata.complexity
    }
  } catch (error) {
    return {
      success: false,
      fileName,
      error: error.message
    }
  }
}

// Scan docs folder recursively
function scanDocsFolder(docsPath) {
  const files = []
  
  function scanDirectory(dirPath) {
    const items = fs.readdirSync(dirPath)
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory()) {
        scanDirectory(fullPath)
      } else if (item.endsWith('.md')) {
        files.push(fullPath)
      }
    }
  }
  
  scanDirectory(docsPath)
  return files
}

// Main analysis function
async function analyzeDocsFolder() {
  console.log('📚 Documentation Analysis')
  console.log('=' .repeat(50))
  
  const docsPath = path.join(process.cwd(), 'docs')
  
  if (!fs.existsSync(docsPath)) {
    console.error('❌ Docs folder not found:', docsPath)
    return false
  }
  
  // Scan all markdown files
  const markdownFiles = scanDocsFolder(docsPath)
  console.log(`\n📄 Found ${markdownFiles.length} markdown files in docs/`)
  
  const analyses = []
  const apiTests = []
  
  // Analyze each file
  for (const filePath of markdownFiles) {
    try {
      const analysis = analyzeMarkdownFile(filePath)
      analyses.push(analysis)
      
      console.log(`\n📝 ${analysis.file}:`)
      console.log(`   Size: ${(analysis.size / 1024).toFixed(2)} KB`)
      console.log(`   Words: ${analysis.words}`)
      console.log(`   Headings: ${analysis.headings.total} (H1:${analysis.headings.h1} H2:${analysis.headings.h2} H3:${analysis.headings.h3})`)
      console.log(`   Features: ${analysis.features.codeBlocks} code blocks, ${analysis.features.links} links`)
      console.log(`   Complexity: ${analysis.complexity}`)
      
      // Test with API parser
      const apiResult = await testAPIWithFile(analysis.content, analysis.file)
      apiTests.push(apiResult)
      
      if (apiResult.success) {
        console.log(`   API Parse: ✅ ${apiResult.nodeCount} nodes, depth ${apiResult.maxDepth}`)
      } else {
        console.log(`   API Parse: ❌ ${apiResult.error}`)
      }
      
    } catch (error) {
      console.error(`   ❌ Error analyzing ${filePath}: ${error.message}`)
    }
  }
  
  // Summary statistics
  console.log('\n📊 Documentation Summary')
  console.log('=' .repeat(50))
  
  const totalFiles = analyses.length
  const totalWords = analyses.reduce((sum, a) => sum + a.words, 0)
  const totalSize = analyses.reduce((sum, a) => sum + a.size, 0)
  const totalHeadings = analyses.reduce((sum, a) => sum + a.headings.total, 0)
  const successfulParsing = apiTests.filter(t => t.success).length
  
  console.log(`📄 Total Files: ${totalFiles}`)
  console.log(`📝 Total Words: ${totalWords.toLocaleString()}`)
  console.log(`💾 Total Size: ${(totalSize / 1024).toFixed(2)} KB`)
  console.log(`📑 Total Headings: ${totalHeadings}`)
  console.log(`⚡ API Success Rate: ${successfulParsing}/${totalFiles} (${((successfulParsing/totalFiles)*100).toFixed(1)}%)`)
  
  // Complexity breakdown
  const complexityBreakdown = analyses.reduce((acc, a) => {
    acc[a.complexity] = (acc[a.complexity] || 0) + 1
    return acc
  }, {})
  
  console.log('\n🎯 Complexity Distribution:')
  Object.entries(complexityBreakdown).forEach(([complexity, count]) => {
    console.log(`   ${complexity}: ${count} files`)
  })
  
  // Feature usage
  const featureUsage = analyses.reduce((acc, a) => {
    Object.entries(a.features).forEach(([feature, count]) => {
      acc[feature] = (acc[feature] || 0) + count
    })
    return acc
  }, {})
  
  console.log('\n🔧 Feature Usage Across All Docs:')
  Object.entries(featureUsage).forEach(([feature, count]) => {
    if (count > 0) {
      console.log(`   ${feature}: ${count}`)
    }
  })
  
  // Largest files
  const largestFiles = analyses
    .sort((a, b) => b.words - a.words)
    .slice(0, 3)
  
  console.log('\n📈 Largest Documents:')
  largestFiles.forEach((file, i) => {
    console.log(`   ${i + 1}. ${file.file} (${file.words} words, ${file.headings.total} headings)`)
  })
  
  // API parsing performance
  const successfulApiTests = apiTests.filter(t => t.success)
  if (successfulApiTests.length > 0) {
    const avgNodes = successfulApiTests.reduce((sum, t) => sum + t.nodeCount, 0) / successfulApiTests.length
    const avgDepth = successfulApiTests.reduce((sum, t) => sum + t.maxDepth, 0) / successfulApiTests.length
    
    console.log('\n🌐 API Parser Performance:')
    console.log(`   Average Nodes: ${avgNodes.toFixed(1)}`)
    console.log(`   Average Depth: ${avgDepth.toFixed(1)}`)
    console.log(`   Files with Code: ${successfulApiTests.filter(t => t.hasCode).length}`)
    console.log(`   Files with Images: ${successfulApiTests.filter(t => t.hasImages).length}`)
  }
  
  return {
    totalFiles,
    totalWords,
    totalSize,
    successfulParsing,
    analyses,
    apiTests
  }
}

// Export for use as module
module.exports = { analyzeDocsFolder, analyzeMarkdownFile, testAPIWithFile, scanDocsFolder }

// Run if called directly
if (require.main === module) {
  analyzeDocsFolder()
    .then(result => {
      console.log('\n🎉 Documentation analysis completed!')
      process.exit(0)
    })
    .catch(error => {
      console.error('\n💥 Analysis failed:', error)
      process.exit(1)
    })
}
