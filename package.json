{"name": "markdown-visualizer", "version": "1.0.0", "description": "Hierarchical Markdown Visualization System", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "generate:component": "plop component", "generate:hook": "plop hook", "generate:module": "plop module", "analyze:bundle": "vite-bundle-analyzer", "lighthouse": "lighthouse http://localhost:5173 --output html --output-path ./lighthouse-report.html"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "d3": "^7.8.5", "framer-motion": "^10.16.4", "highlight.js": "^11.8.0", "katex": "^0.16.8", "lodash.debounce": "^4.0.8", "lodash.throttle": "^4.1.1", "lucide-react": "^0.279.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hotkeys-hook": "^4.4.1", "rehype": "^13.0.1", "rehype-highlight": "^7.0.0", "rehype-katex": "^7.0.0", "remark": "^15.0.1", "remark-frontmatter": "^5.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "remark-parse": "^11.0.0", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "zustand": "^4.4.1"}, "devDependencies": {"@playwright/test": "^1.37.0", "@storybook/addon-essentials": "^7.4.0", "@storybook/react": "^7.4.0", "@storybook/react-vite": "^7.4.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/d3": "^7.4.0", "@types/lodash.debounce": "^4.0.7", "@types/lodash.throttle": "^4.1.7", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "@vitest/coverage-v8": "^0.34.0", "@vitest/ui": "^0.34.0", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "jsdom": "^22.1.0", "playwright": "^1.37.0", "plop": "^4.0.0", "postcss": "^8.4.27", "prettier": "^3.0.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.2", "vite": "^4.4.5", "vite-bundle-analyzer": "^0.7.0", "vitest": "^0.34.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": ["chrome >= 90", "firefox >= 88", "safari >= 14", "edge >= 90"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}