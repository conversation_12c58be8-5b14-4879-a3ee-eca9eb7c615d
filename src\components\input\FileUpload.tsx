import React, { useCallback, useRef, useState } from 'react'
import { useAppStore } from '@/store'
import { parserService } from '@/services/parser.service'
import { getErrorMessage } from '@/services/api.service'
import { DocumentArrowUpIcon } from '@heroicons/react/24/outline'


export const FileUpload: React.FC = () => {
  const { setDocument, setDocumentLoading, setDocumentError } = useAppStore()
  const [isDragOver, setIsDragOver] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelection = useCallback(async (files: FileList) => {
    const file = files[0]
    if (!file) return

    // Validate file
    const maxSize = 10 * 1024 * 1024 // 10MB
    const allowedTypes = ['.md', '.markdown', '.txt']

    if (file.size > maxSize) {
      setDocumentError(`File size exceeds 10MB limit`)
      return
    }

    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
    if (!allowedTypes.includes(fileExtension)) {
      setDocumentError(`Unsupported file type. Please use .md, .markdown, or .txt files`)
      return
    }

    setDocumentLoading(true)
    setDocumentError(null)

    try {
      // Use unified parser service (auto-selects local or API)
      const result = await parserService.parseFile(file, {
        maxDepth: 6,
        includeContent: true,
        extractMetadata: true,
      })

      if (result.tree && result.metadata) {
        // Get the original file content for the raw field
        const text = await file.text()
        setDocument(text, result.tree, result.metadata)
      } else {
        throw new Error('Failed to parse document')
      }
    } catch (error) {
      console.error('File processing error:', error)
      setDocumentError(getErrorMessage(error))
    } finally {
      setDocumentLoading(false)
    }
  }, [setDocument, setDocumentLoading, setDocumentError])

  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    setIsDragOver(false)
    
    const files = event.dataTransfer.files
    if (files.length > 0) {
      handleFileSelection(files)
    }
  }, [handleFileSelection])

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleClick = useCallback(() => {
    fileInputRef.current?.click()
  }, [])

  const handleInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (files) {
      handleFileSelection(files)
    }
    // Reset input value to allow selecting the same file again
    event.target.value = ''
  }, [handleFileSelection])

  return (
    <>
      <input
        ref={fileInputRef}
        type="file"
        accept=".md,.markdown,.txt"
        onChange={handleInputChange}
        className="hidden"
        aria-label="Upload markdown file"
      />
      
      <button
        onClick={handleClick}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        className={`
          btn btn-primary flex items-center space-x-2 transition-all duration-200
          ${isDragOver ? 'bg-primary-700 scale-105' : ''}
        `}
        title="Upload markdown file"
      >
        <DocumentArrowUpIcon className="h-4 w-4" />
        <span className="hidden sm:inline">Upload</span>
      </button>
    </>
  )
}
