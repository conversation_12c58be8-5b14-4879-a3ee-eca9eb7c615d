import React, { useState, useCallback } from 'react'
import { useAppStore } from '@/store'
import { parserService } from '@/services/parser.service'
import { getErrorMessage } from '@/services/api.service'
import { DocumentTextIcon, XMarkIcon } from '@heroicons/react/24/outline'

export const TextInput: React.FC = () => {
  const { setDocument, setDocumentLoading, setDocumentError } = useAppStore()
  const [isOpen, setIsOpen] = useState(false)
  const [content, setContent] = useState('')

  const handleSubmit = useCallback(async () => {
    if (!content.trim()) {
      setDocumentError('Please enter some markdown content')
      return
    }

    setDocumentLoading(true)
    setDocumentError(null)

    try {
      // Use unified parser service (auto-selects local or API)
      const result = await parserService.parseDocument(content, {
        maxDepth: 6,
        includeContent: true,
        extractMetadata: true,
      })

      if (result.tree && result.metadata) {
        setDocument(content, result.tree, result.metadata)
        setIsOpen(false)
        setContent('')
      } else {
        throw new Error('Failed to parse document')
      }
    } catch (error) {
      console.error('Content processing error:', error)
      setDocumentError(getErrorMessage(error))
    } finally {
      setDocumentLoading(false)
    }
  }, [content, setDocument, setDocumentLoading, setDocumentError])

  const handleClose = useCallback(() => {
    setIsOpen(false)
    setContent('')
    setDocumentError(null)
  }, [setDocumentError])

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="btn btn-secondary flex items-center space-x-2"
        title="Enter markdown text"
      >
        <DocumentTextIcon className="h-4 w-4" />
        <span className="hidden sm:inline">Text Input</span>
      </button>
    )
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-strong max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            Enter Markdown Content
          </h2>
          <button
            onClick={handleClose}
            className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700"
            aria-label="Close"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4">
          <div className="mb-4">
            <label 
              htmlFor="markdown-content" 
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >
              Markdown Content
            </label>
            <textarea
              id="markdown-content"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="# Your Markdown Here

## Section 1

Write your markdown content here...

### Subsection

- List item 1
- List item 2

```javascript
console.log('Code blocks are supported!');
```"
              className="w-full h-96 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white font-mono text-sm resize-none"
            />
          </div>

          <div className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            <p>
              <strong>Tip:</strong> You can paste markdown content from any source. 
              Supports headings (H1-H6), lists, code blocks, tables, and more.
            </p>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3">
            <button
              onClick={handleClose}
              className="btn btn-ghost"
            >
              Cancel
            </button>
            <button
              onClick={handleSubmit}
              disabled={!content.trim()}
              className="btn btn-primary"
            >
              Parse Content
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
