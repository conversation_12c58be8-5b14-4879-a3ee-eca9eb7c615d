import React from 'react'
import { useAppStore } from '@/store'
import { FileUpload } from '@components/input/FileUpload'
import { TextInput } from '@components/input/TextInput'
import { ViewToggle } from '@components/interactive/ViewToggle'
import { ThemeToggle } from '@components/interactive/ThemeToggle'
import { ApiStatus } from '@components/ui/ApiStatus'
import { ParserModeIndicator } from '@components/ui/ParserModeIndicator'
import { 
  DocumentTextIcon, 
  Bars3Icon,
  ArrowsPointingOutIcon,
  ArrowsPointingInIcon
} from '@heroicons/react/24/outline'

export const Header: React.FC = () => {
  const { 
    ui, 
    document: doc, 
    toggleSidebar, 
    toggleFullscreen,
    clearDocument 
  } = useAppStore()

  const handleNewDocument = () => {
    if (doc.tree && confirm('Are you sure you want to start a new document? Current work will be lost.')) {
      clearDocument()
    }
  }

  return (
    <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 h-16 flex items-center px-4 lg:px-6">
      <div className="flex items-center justify-between w-full">
        {/* Left section */}
        <div className="flex items-center space-x-4">
          {/* Logo and title */}
          <div className="flex items-center space-x-3">
            <DocumentTextIcon className="h-8 w-8 text-primary-600" />
            <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
              Markdown Visualizer
            </h1>
          </div>

          {/* Menu toggle for mobile */}
          <button
            onClick={toggleSidebar}
            className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
            aria-label="Toggle sidebar"
          >
            <Bars3Icon className="h-5 w-5" />
          </button>
        </div>

        {/* Center section - Document info */}
        <div className="hidden md:flex items-center space-x-4">
          {doc.metadata && (
            <div className="text-sm text-gray-600 dark:text-gray-400">
              <span className="font-medium">{doc.metadata.totalNodes}</span> sections
              <span className="mx-2">•</span>
              <span className="font-medium">{doc.metadata.totalWordCount}</span> words
              <span className="mx-2">•</span>
              <span className="font-medium">{doc.metadata.estimatedReadTime}</span> min read
            </div>
          )}
        </div>

        {/* Right section */}
        <div className="flex items-center space-x-2">
          {/* File upload */}
          <FileUpload />

          {/* Text input */}
          <TextInput />

          {/* New document button */}
          {doc.tree && (
            <button
              onClick={handleNewDocument}
              className="btn btn-ghost text-sm"
              title="New document"
            >
              New
            </button>
          )}

          {/* View toggle */}
          {doc.tree && <ViewToggle />}

          {/* Fullscreen toggle */}
          <button
            onClick={toggleFullscreen}
            className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
            title={ui.isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
          >
            {ui.isFullscreen ? (
              <ArrowsPointingInIcon className="h-5 w-5" />
            ) : (
              <ArrowsPointingOutIcon className="h-5 w-5" />
            )}
          </button>

          {/* Theme toggle */}
          <ThemeToggle />

          {/* Parser Mode Indicator */}
          <ParserModeIndicator />

          {/* API Status */}
          <ApiStatus />
        </div>
      </div>
    </header>
  )
}
