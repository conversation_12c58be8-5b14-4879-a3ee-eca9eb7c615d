import React, { useState, useEffect } from 'react'
import { parserService } from '@/services/parser.service'
import { 
  CpuChipIcon,
  CloudIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline'

interface ParserStatus {
  currentMode: string
  lastUsedMode: 'local' | 'api'
  apiAvailable: boolean
  localAvailable: boolean
  fallbackEnabled: boolean
}

export const ParserModeIndicator: React.FC = () => {
  const [status, setStatus] = useState<ParserStatus | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [showDetails, setShowDetails] = useState(false)

  const checkStatus = async () => {
    setIsLoading(true)
    try {
      const parserStatus = await parserService.getStatus()
      setStatus(parserStatus)
    } catch (error) {
      console.error('Failed to get parser status:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    checkStatus()
    // Check status every 30 seconds
    const interval = setInterval(checkStatus, 30000)
    return () => clearInterval(interval)
  }, [])

  if (!status) {
    return (
      <div className="flex items-center space-x-2 text-sm text-gray-500">
        <ArrowPathIcon className="h-4 w-4 animate-spin" />
        <span className="hidden md:inline">Loading...</span>
      </div>
    )
  }

  const getModeIcon = () => {
    switch (status.lastUsedMode) {
      case 'local':
        return <CpuChipIcon className="h-4 w-4" />
      case 'api':
        return <CloudIcon className="h-4 w-4" />
      default:
        return <Cog6ToothIcon className="h-4 w-4" />
    }
  }

  const getModeColor = () => {
    if (status.currentMode === 'local') {
      return 'text-blue-500'
    }
    if (status.currentMode === 'api' && status.apiAvailable) {
      return 'text-green-500'
    }
    if (status.currentMode === 'auto') {
      return status.apiAvailable ? 'text-green-500' : 'text-yellow-500'
    }
    return 'text-red-500'
  }

  const getModeText = () => {
    const mode = status.currentMode.charAt(0).toUpperCase() + status.currentMode.slice(1)
    const lastUsed = status.lastUsedMode.charAt(0).toUpperCase() + status.lastUsedMode.slice(1)
    
    if (status.currentMode === 'auto') {
      return `Auto (${lastUsed})`
    }
    return mode
  }

  const getStatusIcon = () => {
    if (status.currentMode === 'local') {
      return <CheckCircleIcon className="h-3 w-3 text-blue-500" />
    }
    if (status.apiAvailable) {
      return <CheckCircleIcon className="h-3 w-3 text-green-500" />
    }
    return <ExclamationTriangleIcon className="h-3 w-3 text-yellow-500" />
  }

  return (
    <div className="relative">
      <div 
        className={`flex items-center space-x-2 text-sm cursor-pointer ${getModeColor()}`}
        onClick={() => setShowDetails(!showDetails)}
        title="Click for parser details"
      >
        <div className="flex items-center space-x-1">
          {getModeIcon()}
          <span className="hidden md:inline">{getModeText()}</span>
        </div>
        {getStatusIcon()}
        {isLoading && (
          <ArrowPathIcon className="h-3 w-3 animate-spin" />
        )}
      </div>

      {showDetails && (
        <div className="absolute right-0 top-full mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 min-w-64 z-50">
          <div className="space-y-3">
            <div className="border-b border-gray-200 dark:border-gray-700 pb-2">
              <h3 className="font-semibold text-gray-900 dark:text-white">Parser Status</h3>
            </div>
            
            <div className="space-y-2 text-sm">
              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">Current Mode:</span>
                <span className={`font-medium ${getModeColor()}`}>
                  {status.currentMode.charAt(0).toUpperCase() + status.currentMode.slice(1)}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">Last Used:</span>
                <div className="flex items-center space-x-1">
                  {status.lastUsedMode === 'local' ? (
                    <CpuChipIcon className="h-3 w-3 text-blue-500" />
                  ) : (
                    <CloudIcon className="h-3 w-3 text-green-500" />
                  )}
                  <span className="font-medium">
                    {status.lastUsedMode.charAt(0).toUpperCase() + status.lastUsedMode.slice(1)}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">API Available:</span>
                <div className="flex items-center space-x-1">
                  {status.apiAvailable ? (
                    <CheckCircleIcon className="h-3 w-3 text-green-500" />
                  ) : (
                    <ExclamationTriangleIcon className="h-3 w-3 text-red-500" />
                  )}
                  <span className={status.apiAvailable ? 'text-green-600' : 'text-red-600'}>
                    {status.apiAvailable ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">Local Parser:</span>
                <div className="flex items-center space-x-1">
                  <CheckCircleIcon className="h-3 w-3 text-blue-500" />
                  <span className="text-blue-600">Available</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">Fallback:</span>
                <span className={status.fallbackEnabled ? 'text-green-600' : 'text-gray-600'}>
                  {status.fallbackEnabled ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>

            <div className="border-t border-gray-200 dark:border-gray-700 pt-2">
              <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
                <div>• <strong>Local:</strong> Frontend processing, works offline</div>
                <div>• <strong>API:</strong> Server-side processing, better performance</div>
                <div>• <strong>Auto:</strong> API with local fallback</div>
              </div>
            </div>

            <div className="flex justify-between items-center pt-2">
              <button
                onClick={checkStatus}
                disabled={isLoading}
                className="text-xs text-blue-600 hover:text-blue-700 disabled:opacity-50"
              >
                {isLoading ? 'Refreshing...' : 'Refresh Status'}
              </button>
              <button
                onClick={() => setShowDetails(false)}
                className="text-xs text-gray-500 hover:text-gray-700"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
