import { unified } from 'unified'
import remarkParse from 'remark-parse'
import remarkGfm from 'remark-gfm'
import remarkFrontmatter from 'remark-frontmatter'
import remarkMath from 'remark-math'
import { visit } from 'unist-util-visit'
import type {
  DocumentTree,
  ParseOptions,
  ParseResult,
  ValidationResult,
  DocumentMetadata,
  DocumentStatistics,
  NodeMetadata,
  ValidationError,
  ValidationWarning,
} from '@/types'

// Constants
const MAX_INPUT_SIZE = 10 * 1024 * 1024 // 10MB
const DEFAULT_PARSE_OPTIONS: Required<ParseOptions> = {
  maxDepth: 6,
  includeContent: true,
  preserveWhitespace: false,
  extractMetadata: true,
  customPlugins: [],
}

// Parser Module Implementation
export class ParserModule {
  private processor: any

  constructor() {
    this.processor = unified()
      .use(remarkParse)
      .use(remarkGfm)
      .use(remarkFrontmatter)
      .use(remarkMath)
  }

  async parseDocument(input: string, options: ParseOptions = {}): Promise<ParseResult> {
    const opts = { ...DEFAULT_PARSE_OPTIONS, ...options }
    
    // Validate input
    const validation = this.validateInput(input)
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.map(e => e.message).join(', ')}`)
    }

    try {
      // Preprocess markdown
      const preprocessed = this.preprocessMarkdown(input)
      
      // Parse markdown to AST
      const ast = this.processor.parse(preprocessed)
      const processedAst = await this.processor.run(ast)
      
      // Build tree structure
      const tree = this.buildTree(processedAst, opts)
      
      // Extract metadata and statistics
      const metadata = opts.extractMetadata ? this.extractMetadata(tree) : this.createEmptyMetadata()
      const statistics = this.calculateStatistics(tree)

      return {
        tree,
        metadata,
        statistics,
        errors: [],
        warnings: validation.warnings,
      }
    } catch (error) {
      throw new Error(`Parse failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  validateInput(input: string): ValidationResult {
    const errors: ValidationError[] = []
    const warnings: ValidationWarning[] = []

    // Check input size
    if (input.length > MAX_INPUT_SIZE) {
      errors.push({
        type: 'SIZE_EXCEEDED',
        message: `Input size exceeds maximum limit of ${MAX_INPUT_SIZE} characters`,
      })
    }

    // Check for empty input
    if (!input.trim()) {
      warnings.push({
        type: 'EMPTY_INPUT',
        message: 'Input is empty or contains only whitespace',
      })
    }

    // Check heading structure
    const headingWarnings = this.validateHeadingStructure(input)
    warnings.push(...headingWarnings)

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    }
  }

  private validateHeadingStructure(input: string): ValidationWarning[] {
    const warnings: ValidationWarning[] = []
    const lines = input.split('\n')
    const headings: { level: number; line: number }[] = []

    lines.forEach((line, index) => {
      const match = line.match(/^(#{1,6})\s+(.+)/)
      if (match && match[1]) {
        headings.push({
          level: match[1].length,
          line: index + 1,
        })
      }
    })

    // Check for skipped heading levels
    for (let i = 1; i < headings.length; i++) {
      const currentHeading = headings[i]
      const previousHeading = headings[i - 1]
      
      if (currentHeading && previousHeading) {
        const currentLevel = currentHeading.level
        const previousLevel = previousHeading.level

        if (currentLevel - previousLevel > 1) {
          warnings.push({
            type: 'SKIPPED_HEADING_LEVEL',
            message: `Heading level skipped from H${previousLevel} to H${currentLevel}`,
            line: currentHeading.line,
          })
        }
      }
    }

    return warnings
  }

  preprocessMarkdown(input: string): string {
    // Remove BOM if present
    let processed = input.replace(/^\uFEFF/, '')
    
    // Normalize line endings
    processed = processed.replace(/\r\n/g, '\n').replace(/\r/g, '\n')
    
    // Ensure file ends with newline
    if (!processed.endsWith('\n')) {
      processed += '\n'
    }

    return processed
  }

  buildTree(ast: any, options: Required<ParseOptions>): DocumentTree {
    const root: DocumentTree = {
      id: 'root',
      level: 0,
      title: 'Document Root',
      content: '',
      rawContent: '',
      metadata: this.createEmptyNodeMetadata(),
      children: [],
    }

    const stack: DocumentTree[] = [root]
    const headings: any[] = []

    // Extract all headings from AST
    visit(ast, 'heading', (node: any) => {
      if (node.depth <= options.maxDepth) {
        headings.push(node)
      }
    })

    // Build tree structure
    headings.forEach((heading, index) => {
      const node = this.createNodeFromHeading(heading, ast, index, headings, options)
      this.insertNode(stack, node)
    })

    return root
  }

  private createNodeFromHeading(
    heading: any,
    ast: any,
    index: number,
    allHeadings: any[],
    options: Required<ParseOptions>
  ): DocumentTree {
    const title = this.extractHeadingText(heading)
    const content = options.includeContent ? this.extractAssociatedContent(ast, heading, allHeadings, index) : ''
    
    const node: DocumentTree = {
      id: this.generateNodeId(title, heading.depth),
      level: heading.depth,
      title,
      content,
      rawContent: content, // For now, same as content
      metadata: this.calculateNodeMetadata(content),
      children: [],
    }

    return node
  }

  private extractHeadingText(heading: any): string {
    let text = ''
    visit(heading, 'text', (node: any) => {
      text += node.value
    })
    return text.trim() || 'Untitled'
  }

  private extractAssociatedContent(ast: any, heading: any, allHeadings: any[], headingIndex: number): string {
    const content: string[] = []
    let collecting = false
    const nextHeading = allHeadings[headingIndex + 1]
    const currentLevel = heading.depth

    visit(ast, (node: any) => {
      // Start collecting after current heading
      if (node === heading) {
        collecting = true
        return
      }

      if (collecting) {
        // Stop if we hit a heading of same or higher level
        if (node.type === 'heading' && node.depth <= currentLevel) {
          collecting = false
          return
        }

        // Stop if we hit the next heading
        if (nextHeading && node === nextHeading) {
          collecting = false
          return
        }

        // Collect content
        if (node.type === 'paragraph' || node.type === 'list' || node.type === 'code' || node.type === 'blockquote') {
          content.push(this.nodeToMarkdown(node))
        }
      }
    })

    return content.join('\n\n').trim()
  }

  private nodeToMarkdown(node: any): string {
    // Simple conversion - in a real implementation, this would be more comprehensive
    switch (node.type) {
      case 'paragraph':
        return this.extractTextFromNode(node)
      case 'list':
        return this.listToMarkdown(node)
      case 'code':
        return `\`\`\`${node.lang || ''}\n${node.value}\n\`\`\``
      case 'blockquote':
        return `> ${this.extractTextFromNode(node)}`
      default:
        return this.extractTextFromNode(node)
    }
  }

  private extractTextFromNode(node: any): string {
    let text = ''
    visit(node, 'text', (textNode: any) => {
      text += textNode.value
    })
    return text
  }

  private listToMarkdown(node: any): string {
    // Simplified list conversion
    const items: string[] = []
    if (node.children) {
      node.children.forEach((item: any, index: number) => {
        const text = this.extractTextFromNode(item)
        const prefix = node.ordered ? `${index + 1}. ` : '- '
        items.push(prefix + text)
      })
    }
    return items.join('\n')
  }

  private insertNode(stack: DocumentTree[], node: DocumentTree): void {
    // Find the correct parent based on heading level
    while (stack.length > 1) {
      const lastNode = stack[stack.length - 1]
      if (lastNode && lastNode.level >= node.level) {
        stack.pop()
      } else {
        break
      }
    }

    const parent = stack[stack.length - 1]
    if (parent) {
      node.parent = parent
      parent.children.push(node)
    }
    stack.push(node)
  }

  private generateNodeId(title: string, level: number): string {
    const slug = title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 50)

    return `${level}-${slug}-${Math.random().toString(36).substr(2, 9)}`
  }

  calculateNodeMetadata(content: string): NodeMetadata {
    const wordCount = this.countWords(content)
    const characterCount = content.length

    return {
      wordCount,
      characterCount,
      hasImages: /!\[.*?\]\(.*?\)/.test(content),
      hasLinks: /\[.*?\]\(.*?\)/.test(content),
      hasCode: /```[\s\S]*?```|`[^`]+`/.test(content),
      hasTables: /\|.*\|/.test(content),
      hasLists: /^[\s]*[-*+]\s|^[\s]*\d+\.\s/m.test(content),
      complexity: this.calculateComplexity(content),
      estimatedReadTime: Math.ceil(wordCount / 200), // 200 words per minute
    }
  }

  private countWords(content: string): number {
    return content
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 0)
      .length
  }

  private calculateComplexity(content: string): 'low' | 'medium' | 'high' {
    const wordCount = this.countWords(content)
    const hasComplexElements = /```[\s\S]*?```|\|.*\|/.test(content)

    if (wordCount < 50 && !hasComplexElements) return 'low'
    if (wordCount < 200 && !hasComplexElements) return 'medium'
    return 'high'
  }

  extractMetadata(tree: DocumentTree): DocumentMetadata {
    const stats = this.calculateStatistics(tree)

    return {
      title: tree.children[0]?.title || 'Untitled Document',
      totalNodes: stats.nodeCount,
      maxDepth: stats.maxDepth,
      totalWordCount: this.getTotalWordCount(tree),
      estimatedReadTime: Math.ceil(this.getTotalWordCount(tree) / 200),
      hasImages: this.hasContentType(tree, 'hasImages'),
      hasCode: this.hasContentType(tree, 'hasCode'),
      hasTables: this.hasContentType(tree, 'hasTables'),
      complexity: this.getOverallComplexity(tree),
    }
  }

  calculateStatistics(tree: DocumentTree): DocumentStatistics {
    const nodeCount = this.countNodes(tree)
    const maxDepth = this.calculateMaxDepth(tree)
    const avgBranchingFactor = this.calculateBranchingFactor(tree)
    const contentDistribution = this.analyzeContentDistribution(tree)

    return {
      nodeCount,
      maxDepth,
      avgBranchingFactor,
      contentDistribution,
    }
  }

  private countNodes(tree: DocumentTree): number {
    let count = tree.level > 0 ? 1 : 0 // Don't count root
    for (const child of tree.children) {
      count += this.countNodes(child)
    }
    return count
  }

  private calculateMaxDepth(tree: DocumentTree): number {
    let maxDepth = tree.level
    for (const child of tree.children) {
      maxDepth = Math.max(maxDepth, this.calculateMaxDepth(child))
    }
    return maxDepth
  }

  private calculateBranchingFactor(tree: DocumentTree): number {
    const nodes = this.getAllNodes(tree)
    const totalChildren = nodes.reduce((sum, node) => sum + node.children.length, 0)
    return nodes.length > 0 ? totalChildren / nodes.length : 0
  }

  private analyzeContentDistribution(tree: DocumentTree) {
    const byLevel: Record<number, number> = {}
    const byComplexity: Record<string, number> = { low: 0, medium: 0, high: 0 }
    const byContentType: Record<string, number> = {}

    const traverse = (node: DocumentTree) => {
      if (node.level > 0) {
        byLevel[node.level] = (byLevel[node.level] || 0) + 1
        const complexity = node.metadata.complexity
        if (complexity && complexity in byComplexity) {
          byComplexity[complexity] = (byComplexity[complexity] || 0) + 1
        }

        if (node.metadata.hasCode) byContentType.code = (byContentType.code || 0) + 1
        if (node.metadata.hasImages) byContentType.images = (byContentType.images || 0) + 1
        if (node.metadata.hasTables) byContentType.tables = (byContentType.tables || 0) + 1
        if (node.metadata.hasLists) byContentType.lists = (byContentType.lists || 0) + 1
      }

      node.children.forEach(traverse)
    }

    traverse(tree)

    return { byLevel, byComplexity, byContentType }
  }

  private getAllNodes(tree: DocumentTree): DocumentTree[] {
    const nodes: DocumentTree[] = []
    if (tree.level > 0) nodes.push(tree)

    for (const child of tree.children) {
      nodes.push(...this.getAllNodes(child))
    }

    return nodes
  }

  private getTotalWordCount(tree: DocumentTree): number {
    let total = tree.metadata.wordCount
    for (const child of tree.children) {
      total += this.getTotalWordCount(child)
    }
    return total
  }

  private hasContentType(tree: DocumentTree, type: keyof NodeMetadata): boolean {
    if (tree.metadata[type] as boolean) return true
    return tree.children.some(child => this.hasContentType(child, type))
  }

  private getOverallComplexity(tree: DocumentTree): 'low' | 'medium' | 'high' {
    const nodes = this.getAllNodes(tree)
    const complexityScores = { low: 1, medium: 2, high: 3 }
    const avgScore = nodes.reduce((sum, node) => sum + complexityScores[node.metadata.complexity], 0) / nodes.length

    if (avgScore < 1.5) return 'low'
    if (avgScore < 2.5) return 'medium'
    return 'high'
  }

  private createEmptyMetadata(): DocumentMetadata {
    return {
      title: 'Empty Document',
      totalNodes: 0,
      maxDepth: 0,
      totalWordCount: 0,
      estimatedReadTime: 0,
      hasImages: false,
      hasCode: false,
      hasTables: false,
      complexity: 'low',
    }
  }

  private createEmptyNodeMetadata(): NodeMetadata {
    return {
      wordCount: 0,
      characterCount: 0,
      hasImages: false,
      hasLinks: false,
      hasCode: false,
      hasTables: false,
      hasLists: false,
      complexity: 'low',
      estimatedReadTime: 0,
    }
  }
}

// Export singleton instance
export const parserModule = new ParserModule()
