import type { ParseResult, ParseOptions } from '@/types'
import { parserModule } from '@/modules/parser'
import { apiService, isNetworkError, isTimeoutError } from '@/services/api.service'

// Environment configuration
const USE_LOCAL_PARSER = (import.meta as any).env?.VITE_USE_LOCAL_PARSER === 'true'
const FALLBACK_TO_LOCAL = (import.meta as any).env?.VITE_FALLBACK_TO_LOCAL !== 'false' // Default true

// Parser mode type
export type ParserMode = 'local' | 'api' | 'auto'

// Parser service configuration
interface ParserConfig {
  mode: ParserMode
  timeout: number
  retries: number
  fallbackEnabled: boolean
}

const DEFAULT_CONFIG: ParserConfig = {
  mode: USE_LOCAL_PARSER ? 'local' : 'api',
  timeout: 30000, // 30 seconds
  retries: 1,
  fallbackEnabled: FALLBACK_TO_LOCAL,
}

// Parser service class
class ParserService {
  private config: ParserConfig
  private lastUsedMode: 'local' | 'api' = 'local'

  constructor(config: Partial<ParserConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
    this.lastUsedMode = this.config.mode === 'local' ? 'local' : 'api'
  }

  /**
   * Parse markdown content with automatic mode selection
   */
  async parseDocument(content: string, options?: ParseOptions): Promise<ParseResult> {
    const startTime = Date.now()
    
    try {
      let result: ParseResult

      switch (this.config.mode) {
        case 'local':
          result = await this.parseWithLocal(content, options)
          this.lastUsedMode = 'local'
          break
          
        case 'api':
          result = await this.parseWithAPI(content, options)
          this.lastUsedMode = 'api'
          break
          
        case 'auto':
          result = await this.parseWithAuto(content, options)
          break
          
        default:
          throw new Error(`Invalid parser mode: ${this.config.mode}`)
      }

      const duration = Date.now() - startTime
      console.log(`✅ Parsing completed using ${this.lastUsedMode} parser in ${duration}ms`)
      
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      console.error(`❌ Parsing failed after ${duration}ms:`, error)
      throw error
    }
  }

  /**
   * Parse uploaded file with automatic mode selection
   */
  async parseFile(file: File, options?: ParseOptions): Promise<ParseResult> {
    const startTime = Date.now()
    
    try {
      let result: ParseResult

      switch (this.config.mode) {
        case 'local':
          result = await this.parseFileWithLocal(file, options)
          this.lastUsedMode = 'local'
          break
          
        case 'api':
          result = await this.parseFileWithAPI(file, options)
          this.lastUsedMode = 'api'
          break
          
        case 'auto':
          result = await this.parseFileWithAuto(file, options)
          break
          
        default:
          throw new Error(`Invalid parser mode: ${this.config.mode}`)
      }

      const duration = Date.now() - startTime
      console.log(`✅ File parsing completed using ${this.lastUsedMode} parser in ${duration}ms`)
      
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      console.error(`❌ File parsing failed after ${duration}ms:`, error)
      throw error
    }
  }

  /**
   * Parse content using local parser
   */
  private async parseWithLocal(content: string, options?: ParseOptions): Promise<ParseResult> {
    console.log('🔧 Using local parser...')
    return await parserModule.parseDocument(content, options)
  }

  /**
   * Parse content using API
   */
  private async parseWithAPI(content: string, options?: ParseOptions): Promise<ParseResult> {
    console.log('🌐 Using API parser...')
    return await apiService.parseContent(content, options)
  }

  /**
   * Parse content with automatic fallback
   */
  private async parseWithAuto(content: string, options?: ParseOptions): Promise<ParseResult> {
    // Try API first, fallback to local if needed
    try {
      console.log('🌐 Trying API parser first...')
      const result = await this.parseWithAPI(content, options)
      this.lastUsedMode = 'api'
      return result
    } catch (error) {
      if (this.config.fallbackEnabled && this.shouldFallbackToLocal(error)) {
        console.log('🔧 API failed, falling back to local parser...')
        const result = await this.parseWithLocal(content, options)
        this.lastUsedMode = 'local'
        return result
      }
      throw error
    }
  }

  /**
   * Parse file using local parser
   */
  private async parseFileWithLocal(file: File, options?: ParseOptions): Promise<ParseResult> {
    console.log('🔧 Using local parser for file...')
    const content = await file.text()
    return await parserModule.parseDocument(content, options)
  }

  /**
   * Parse file using API
   */
  private async parseFileWithAPI(file: File, options?: ParseOptions): Promise<ParseResult> {
    console.log('🌐 Using API parser for file...')
    return await apiService.parseFile(file, options)
  }

  /**
   * Parse file with automatic fallback
   */
  private async parseFileWithAuto(file: File, options?: ParseOptions): Promise<ParseResult> {
    // Try API first, fallback to local if needed
    try {
      console.log('🌐 Trying API parser for file first...')
      const result = await this.parseFileWithAPI(file, options)
      this.lastUsedMode = 'api'
      return result
    } catch (error) {
      if (this.config.fallbackEnabled && this.shouldFallbackToLocal(error)) {
        console.log('🔧 API failed, falling back to local parser for file...')
        const result = await this.parseFileWithLocal(file, options)
        this.lastUsedMode = 'local'
        return result
      }
      throw error
    }
  }

  /**
   * Determine if we should fallback to local parser
   */
  private shouldFallbackToLocal(error: unknown): boolean {
    // Fallback for network-related errors
    if (isNetworkError(error) || isTimeoutError(error)) {
      return true
    }

    // Fallback for API unavailable
    if (error instanceof Error && error.message.includes('fetch')) {
      return true
    }

    // Don't fallback for validation errors (they would fail locally too)
    return false
  }

  /**
   * Check if API is available
   */
  async isAPIAvailable(): Promise<boolean> {
    try {
      return await apiService.isAvailable()
    } catch {
      return false
    }
  }

  /**
   * Get current parser mode
   */
  getCurrentMode(): ParserMode {
    return this.config.mode
  }

  /**
   * Get last used parser
   */
  getLastUsedMode(): 'local' | 'api' {
    return this.lastUsedMode
  }

  /**
   * Update parser configuration
   */
  updateConfig(newConfig: Partial<ParserConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * Get parser status information
   */
  async getStatus() {
    const apiAvailable = await this.isAPIAvailable()
    
    return {
      currentMode: this.config.mode,
      lastUsedMode: this.lastUsedMode,
      apiAvailable,
      localAvailable: true, // Local parser is always available
      fallbackEnabled: this.config.fallbackEnabled,
      config: this.config,
    }
  }
}

// Export singleton instance
export const parserService = new ParserService()

// Export utility functions
export const getParserMode = (): ParserMode => {
  return USE_LOCAL_PARSER ? 'local' : 'api'
}

export const isLocalParserEnabled = (): boolean => {
  return USE_LOCAL_PARSER
}

export const isFallbackEnabled = (): boolean => {
  return FALLBACK_TO_LOCAL
}

// Export types
export type { ParserConfig }
