// Verification Module for Hybrid Parser Testing
// Tests both local and API parsers with detailed breakdown analysis

const fs = require('fs')
const path = require('path')

const API_BASE_URL = 'http://localhost:3001'

// Test configuration
const TEST_CONFIG = {
  sampleFile: 'public/sample.md',
  docsFolder: 'docs/',
  options: {
    maxDepth: 6,
    includeContent: true,
    extractMetadata: true
  }
}

// Utility functions
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function formatDuration(ms) {
  if (ms < 1000) return `${ms}ms`
  return `${(ms / 1000).toFixed(2)}s`
}

// Markdown analysis functions
function analyzeMarkdownContent(content) {
  const lines = content.split('\n')
  const analysis = {
    totalLines: lines.length,
    totalCharacters: content.length,
    totalWords: content.split(/\s+/).filter(word => word.length > 0).length,
    
    // Heading analysis
    headings: {
      h1: (content.match(/^# /gm) || []).length,
      h2: (content.match(/^## /gm) || []).length,
      h3: (content.match(/^### /gm) || []).length,
      h4: (content.match(/^#### /gm) || []).length,
      h5: (content.match(/^##### /gm) || []).length,
      h6: (content.match(/^###### /gm) || []).length,
    },
    
    // Content features
    features: {
      codeBlocks: (content.match(/```[\s\S]*?```/g) || []).length,
      inlineCode: (content.match(/`[^`]+`/g) || []).length,
      links: (content.match(/\[([^\]]+)\]\(([^)]+)\)/g) || []).length,
      images: (content.match(/!\[([^\]]*)\]\(([^)]+)\)/g) || []).length,
      tables: (content.match(/\|.*\|/g) || []).length,
      lists: (content.match(/^[\s]*[-*+]\s/gm) || []).length,
      orderedLists: (content.match(/^[\s]*\d+\.\s/gm) || []).length,
      blockquotes: (content.match(/^>/gm) || []).length,
      horizontalRules: (content.match(/^---+$/gm) || []).length,
      boldText: (content.match(/\*\*[^*]+\*\*/g) || []).length,
      italicText: (content.match(/\*[^*]+\*/g) || []).length,
    }
  }
  
  analysis.headings.total = Object.values(analysis.headings).reduce((sum, count) => sum + count, 0)
  analysis.estimatedReadTime = Math.ceil(analysis.totalWords / 200) // 200 words per minute
  
  return analysis
}

// API Parser Test
async function testAPIParser(content, options) {
  console.log('🌐 Testing API Parser...')
  const startTime = Date.now()
  
  try {
    const response = await fetch(`${API_BASE_URL}/api/parse`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ content, options })
    })
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`)
    }
    
    const data = await response.json()
    const duration = Date.now() - startTime
    
    if (!data.success) {
      throw new Error(`Parse Error: ${data.error?.message || 'Unknown error'}`)
    }
    
    return {
      success: true,
      duration,
      result: data.data,
      mode: 'api'
    }
  } catch (error) {
    return {
      success: false,
      duration: Date.now() - startTime,
      error: error.message,
      mode: 'api'
    }
  }
}

// Local Parser Test (simulated - we'll analyze the expected structure)
function simulateLocalParser(content, options) {
  console.log('🔧 Simulating Local Parser Analysis...')
  const startTime = Date.now()
  
  try {
    // Simulate local parsing by analyzing markdown structure
    const analysis = analyzeMarkdownContent(content)
    
    // Create expected tree structure based on headings
    const lines = content.split('\n')
    const headings = []
    let currentContent = ''
    
    lines.forEach((line, index) => {
      const headingMatch = line.match(/^(#{1,6})\s+(.+)$/)
      if (headingMatch) {
        if (currentContent.trim()) {
          // Add content to previous heading
          if (headings.length > 0) {
            headings[headings.length - 1].content = currentContent.trim()
          }
          currentContent = ''
        }
        
        headings.push({
          level: headingMatch[1].length,
          title: headingMatch[2].trim(),
          content: '',
          lineNumber: index + 1
        })
      } else if (headings.length > 0) {
        currentContent += line + '\n'
      }
    })
    
    // Add final content
    if (currentContent.trim() && headings.length > 0) {
      headings[headings.length - 1].content = currentContent.trim()
    }
    
    const duration = Date.now() - startTime
    
    return {
      success: true,
      duration,
      result: {
        tree: {
          id: 'root',
          level: 0,
          title: 'Document Root',
          children: headings.map((h, i) => ({
            id: `${h.level}-${h.title.toLowerCase().replace(/\s+/g, '-')}-${i}`,
            level: h.level,
            title: h.title,
            content: h.content,
            metadata: {
              wordCount: h.content.split(/\s+/).filter(w => w.length > 0).length,
              characterCount: h.content.length,
              lineNumber: h.lineNumber
            }
          }))
        },
        metadata: {
          title: headings.length > 0 ? headings[0].title : 'Untitled',
          totalNodes: headings.length,
          maxDepth: Math.max(...headings.map(h => h.level), 0),
          totalWordCount: analysis.totalWords,
          estimatedReadTime: analysis.estimatedReadTime,
          hasImages: analysis.features.images > 0,
          hasCode: analysis.features.codeBlocks > 0 || analysis.features.inlineCode > 0,
          hasTables: analysis.features.tables > 0,
          complexity: analysis.headings.total > 20 ? 'high' : analysis.headings.total > 10 ? 'medium' : 'low'
        },
        statistics: {
          nodeCount: headings.length,
          maxDepth: Math.max(...headings.map(h => h.level), 0),
          avgBranchingFactor: headings.length > 0 ? headings.length / Math.max(...headings.map(h => h.level), 1) : 0
        }
      },
      mode: 'local'
    }
  } catch (error) {
    return {
      success: false,
      duration: Date.now() - startTime,
      error: error.message,
      mode: 'local'
    }
  }
}

// Compare parser results
function compareResults(apiResult, localResult) {
  console.log('\n📊 Parser Comparison Analysis')
  console.log('=' .repeat(50))
  
  if (!apiResult.success || !localResult.success) {
    console.log('❌ Cannot compare - one or both parsers failed')
    return false
  }
  
  const api = apiResult.result
  const local = localResult.result
  
  // Performance comparison
  console.log('\n⚡ Performance Metrics:')
  console.log(`   API Parser:   ${formatDuration(apiResult.duration)}`)
  console.log(`   Local Parser: ${formatDuration(localResult.duration)}`)
  console.log(`   Speed Ratio:  ${(localResult.duration / apiResult.duration).toFixed(2)}x`)
  
  // Structure comparison
  console.log('\n🏗️  Structure Comparison:')
  console.log(`   API Nodes:    ${api.statistics.nodeCount}`)
  console.log(`   Local Nodes:  ${local.statistics.nodeCount}`)
  console.log(`   API Depth:    ${api.statistics.maxDepth}`)
  console.log(`   Local Depth:  ${local.statistics.maxDepth}`)
  
  // Metadata comparison
  console.log('\n📋 Metadata Comparison:')
  console.log(`   API Title:    "${api.metadata.title}"`)
  console.log(`   Local Title:  "${local.metadata.title}"`)
  console.log(`   API Words:    ${api.metadata.totalWordCount}`)
  console.log(`   Local Words:  ${local.metadata.totalWordCount}`)
  
  // Feature detection comparison
  console.log('\n🎯 Feature Detection:')
  console.log(`   API - Code:   ${api.metadata.hasCode}`)
  console.log(`   Local - Code: ${local.metadata.hasCode}`)
  console.log(`   API - Images: ${api.metadata.hasImages}`)
  console.log(`   Local - Images: ${local.metadata.hasImages}`)
  
  // Compatibility score
  const compatibilityChecks = [
    api.statistics.nodeCount === local.statistics.nodeCount,
    api.statistics.maxDepth === local.statistics.maxDepth,
    api.metadata.title === local.metadata.title,
    Math.abs(api.metadata.totalWordCount - local.metadata.totalWordCount) <= 5, // Allow small variance
    api.metadata.hasCode === local.metadata.hasCode,
    api.metadata.hasImages === local.metadata.hasImages
  ]
  
  const compatibilityScore = (compatibilityChecks.filter(Boolean).length / compatibilityChecks.length) * 100
  
  console.log('\n🎯 Compatibility Score:')
  console.log(`   ${compatibilityScore.toFixed(1)}% compatible`)
  
  if (compatibilityScore >= 90) {
    console.log('   ✅ Excellent compatibility')
  } else if (compatibilityScore >= 75) {
    console.log('   ⚠️  Good compatibility with minor differences')
  } else {
    console.log('   ❌ Poor compatibility - significant differences detected')
  }
  
  return compatibilityScore >= 75
}

// Main verification function
async function runVerification() {
  console.log('🚀 Starting Hybrid Parser Verification')
  console.log('=' .repeat(50))
  
  // Load sample file
  const samplePath = path.join(process.cwd(), TEST_CONFIG.sampleFile)
  if (!fs.existsSync(samplePath)) {
    console.error('❌ Sample file not found:', samplePath)
    return false
  }
  
  const content = fs.readFileSync(samplePath, 'utf8')
  const fileSize = Buffer.byteLength(content, 'utf8')
  
  console.log('\n📄 Document Analysis:')
  console.log(`   File: ${TEST_CONFIG.sampleFile}`)
  console.log(`   Size: ${formatBytes(fileSize)}`)
  
  // Analyze markdown content
  const analysis = analyzeMarkdownContent(content)
  console.log(`   Lines: ${analysis.totalLines}`)
  console.log(`   Words: ${analysis.totalWords}`)
  console.log(`   Characters: ${analysis.totalCharacters}`)
  console.log(`   Read Time: ${analysis.estimatedReadTime} minutes`)
  
  console.log('\n📑 Content Breakdown:')
  console.log(`   Headings: H1(${analysis.headings.h1}) H2(${analysis.headings.h2}) H3(${analysis.headings.h3}) H4(${analysis.headings.h4}) H5(${analysis.headings.h5}) H6(${analysis.headings.h6})`)
  console.log(`   Code Blocks: ${analysis.features.codeBlocks}`)
  console.log(`   Inline Code: ${analysis.features.inlineCode}`)
  console.log(`   Links: ${analysis.features.links}`)
  console.log(`   Images: ${analysis.features.images}`)
  console.log(`   Lists: ${analysis.features.lists} unordered, ${analysis.features.orderedLists} ordered`)
  console.log(`   Tables: ${analysis.features.tables}`)
  console.log(`   Formatting: ${analysis.features.boldText} bold, ${analysis.features.italicText} italic`)
  
  // Test both parsers
  console.log('\n🧪 Running Parser Tests...')
  
  const [apiResult, localResult] = await Promise.all([
    testAPIParser(content, TEST_CONFIG.options),
    Promise.resolve(simulateLocalParser(content, TEST_CONFIG.options))
  ])
  
  // Display individual results
  console.log('\n🌐 API Parser Results:')
  if (apiResult.success) {
    console.log(`   ✅ Success in ${formatDuration(apiResult.duration)}`)
    console.log(`   📊 Parsed ${apiResult.result.statistics.nodeCount} nodes`)
    console.log(`   📏 Max depth: ${apiResult.result.statistics.maxDepth}`)
    console.log(`   📝 Title: "${apiResult.result.metadata.title}"`)
  } else {
    console.log(`   ❌ Failed: ${apiResult.error}`)
  }
  
  console.log('\n🔧 Local Parser Results:')
  if (localResult.success) {
    console.log(`   ✅ Success in ${formatDuration(localResult.duration)}`)
    console.log(`   📊 Parsed ${localResult.result.statistics.nodeCount} nodes`)
    console.log(`   📏 Max depth: ${localResult.result.statistics.maxDepth}`)
    console.log(`   📝 Title: "${localResult.result.metadata.title}"`)
  } else {
    console.log(`   ❌ Failed: ${localResult.error}`)
  }
  
  // Compare results
  const isCompatible = compareResults(apiResult, localResult)
  
  console.log('\n🎯 Verification Summary:')
  console.log(`   API Parser: ${apiResult.success ? '✅ Working' : '❌ Failed'}`)
  console.log(`   Local Parser: ${localResult.success ? '✅ Working' : '❌ Failed'}`)
  console.log(`   Compatibility: ${isCompatible ? '✅ Compatible' : '❌ Issues detected'}`)
  
  return apiResult.success && localResult.success && isCompatible
}

// Export for use as module
module.exports = { runVerification, analyzeMarkdownContent, testAPIParser, simulateLocalParser, compareResults }

// Run if called directly
if (require.main === module) {
  runVerification()
    .then(success => {
      console.log(success ? '\n🎉 Verification completed successfully!' : '\n⚠️  Verification completed with issues')
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      console.error('\n💥 Verification failed:', error)
      process.exit(1)
    })
}
